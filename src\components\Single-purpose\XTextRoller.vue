<template>
  <!-- 大转盘专用 底部右侧文本滚动组件 -->
  <div class="scroll-board" ref="boxRef">
    <div
      class="scroll-list"
      :style="{
        transform: `translateY(-${scrollTop}px)`,
        transition: isAnimating ? 'transform 1s' : 'none',
      }"
      @transitionend="onTransitionEnd"
    >
      <div v-for="(item, idx) in rollerList" :key="idx" class="scroll-item">
        <span class="user">{{ item[option.user] }}</span>
        <span class="amount"
          ><span class="green">₱{{ item[option.amount] }}</span></span
        >
        <span class="time">{{ item[option.time] }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from "vue";

const props = defineProps({
  rollerList: { type: Array, default: [] },
  option: {
    type: Object,
    default: {
      user: "user_id",
      amount: "prize_amount",
      time: "prize_time",
    },
  },
  getMore: {
    type: Function,
    default: () => {},
  },
});

// 拼接一份到末尾，实现无缝滚动
// const displayList = computed(() => [...props.rollerList, ...props.rollerList.slice(0, 2)]);
const boxRef = ref<HTMLElement | null>(null);
const itemHeight = ref(0);
const scrollTop = ref(0);
const isAnimating = ref(false);
let timer: any = null;
let currentIndex = 0;

const getMoreData = async () => {
  await props.getMore?.();
  // 获取数据后重新开始计时动画
  if (currentIndex < props.rollerList.length - 4) startScroll();
};

const startScroll = () => {
  timer = setInterval(() => {
    isAnimating.value = true;
    currentIndex++;
    scrollTop.value = currentIndex * itemHeight.value;
    // 到达最后两行，停止动画，重新请求数据
    if (currentIndex === props.rollerList.length - 4) {
      setTimeout(() => {
        clearInterval(timer);
        timer = null;
        isAnimating.value = false;
        getMoreData();
        /*  scrollTop.value = 0;
        currentIndex = 0;
        props.getMore?.();
        startScroll(); */
      }, 500); // 等待动画结束
    }
  }, 1500);
};

const onTransitionEnd = async () => {
  // 这里可做扩展
};

onMounted(() => {
  // 获取单行高度
  setTimeout(() => {
    const item = boxRef.value?.querySelector(".scroll-item") as HTMLElement;
    if (item) {
      itemHeight.value = item.offsetHeight;
      startScroll();
    }
  }, 2000);
});

onBeforeUnmount(() => {
  clearInterval(timer);
  timer = null;
});
</script>

<style scoped>
.scroll-board {
  height: 40px; /* 两行高度 */
  overflow: hidden;
  border-radius: 8px;
  padding: 2px 0;
  box-sizing: border-box;
  font-size: 10px;
  margin-top: 6px;
}
.scroll-list {
  will-change: transform;
}
.scroll-item {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 20px;
  line-height: 20px;
  color: #fff;
  padding: 0 8px;
  box-sizing: border-box;
  font-weight: bold;
  gap: 2px;
}
.user {
  /* flex: 1 1 60px; */
}
.amount {
  /* flex: 1 1 60px; */
  /* text-align: left; */
}
.green {
  color: #00f570;
}
.time {
  /* flex: 0 0 40px; */
  /* text-align: right; */
  /* opacity: 0.8; */
}
</style>
