<template>
  <div class="phone-input-container">
    <div class="input-wrap">
      <span class="phone-code">+63</span>
      <input
        class="input"
        autofocus
        id="phone"
        maxlength="10"
        v-model="inputValue"
        type="number"
        inputmode="numeric"
        @blur="validatePhone"
        @input="handleInput"
        :placeholder="placeholder"
      />
      <ZIcon v-show="inputValue" type="icon-close" size="16" color="#999" @click="clearInput" />
    </div>
    <div v-show="errorType === 'empty'" class="null-tip">{{ errorMessages.empty }}</div>
    <div v-show="errorType === 'format'" class="null-tip">{{ errorMessages.format }}</div>
    <div v-show="errorType === 'noRegister'" class="null-tip">{{ errorMessages.noRegister }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineProps, defineEmits } from "vue";
import { setLocalStorage, getLocalStorage } from "@/utils/core/Storage";
import { isPhilippinePhoneNumber } from "@/utils/core/tools";
import { onMounted } from "vue";

interface Props {
  modelValue?: string;
  placeholder?: string;
  errorMessages?: {
    empty: string;
    format: string;
    noRegister: string;
  };
  validateOnInput?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  placeholder: "Enter Your Phone Number",
  errorMessages: () => ({
    empty: "Please enter the phone number",
    format: "Please enter a valid 10-digit mobile phone number",
    noRegister: "Please register an account first",
  }),
  validateOnInput: false,
});

const emits = defineEmits<{
  "update:modelValue": [value: string];
  validityChange: [isValid: boolean];
}>();

const inputValue = ref(props.modelValue);
const errorType = ref("");

// 计算属性：手机号是否有效
const isValid = computed(() => {
  if (!inputValue.value) return false;
  return isPhilippinePhoneNumber(inputValue.value);
});

onMounted(() => {
  const phoneNum = getLocalStorage("phone");
  if (phoneNum) {
    inputValue.value = phoneNum;
    emits("update:modelValue", phoneNum + "");
  }
});

// 监听外部值变化
watch(
  () => props.modelValue,
  (newVal) => {
    inputValue.value = newVal;
    validatePhone();
  }
);
// 清除输入框内容
const clearInput = () => {
  inputValue.value = "";
  errorType.value = "";
  emits("update:modelValue", "");
  emits("validityChange", false);
};

// 处理输入事件
const handleInput = (e: Event) => {
  const target = e.target as HTMLInputElement;
  const val = target.value;
  errorType.value = "";

  // 限制最大长度
  if (val.length > 10) {
    inputValue.value = val.substring(0, 10);
  } else {
    inputValue.value = val;
  }

  // 同步到父组件
  emits("update:modelValue", inputValue.value);

  // 实时验证
  if (props.validateOnInput) {
    validatePhone();
  }

  // 通知父组件有效性变化
  emits("validityChange", isValid.value);
};

// 验证手机号
const validatePhone = () => {
  if (!inputValue.value) {
    errorType.value = "empty";
  } else if (!isPhilippinePhoneNumber(inputValue.value)) {
    errorType.value = "format";
  } else {
    errorType.value = "";
    setLocalStorage("phone", inputValue.value);
  }

  // 通知父组件有效性变化
  emits("validityChange", isValid.value);
};
</script>

<style scoped lang="scss">
.phone-input-container {
  .input-wrap {
    width: 100%;
    height: 30px;
    line-height: 30px;
    border-bottom: 1px solid #ddd;
    outline: none;
    display: flex;
    align-items: center;

    .phone-code {
      font-size: 16px;
      line-height: 14px;
      color: #222;
      border-right: 1px solid #ddd;
      padding-right: 6px;
      white-space: nowrap;
      font-weight: 600;
      font-family: D-DIN;
      font-size: 18px;
    }

    .input {
      width: 100%;
      margin-left: 6px;
      flex: 1;
      border: none;
      outline: none;
      background: transparent;
      font-size: 16px;
      font-weight: 600;
      color: #222;
      font-family: D-DIN;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;

      &::placeholder {
        color: #c0c0c0;
        /* 输入框内默认文字 */
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }
  }

  .null-tip {
    margin-top: 4px;
    color: var(--Nustar, #ac1140);
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
}
</style>
