<!-- 游戏加载页面 -->
<template>
  <div class="loading-mask">
    <RedNuStar />
    <ZIcon type="icon-fanhui" color="" :size="20" class="icon-fanhui" @click="handleBack"></ZIcon>
    <div class="circle-wrap">
      <!-- 使用公用的 CircleProgress 组件 -->
      <CircleProgress v-model="currentProgress" :size="80" bottom-text="Loading…" :animated="true" :speed="50" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import RedNuStar from '@/assets/RedNuStar.svg'
import CircleProgress from '@/components/CircleProgress.vue';

// 定义 props
const props = defineProps({
  progress: {
    type: Number,
    default: 0
  }
});

// 定义 emits
const emit = defineEmits(['back']);

// 当前进度值
const currentProgress = ref(0);

// 监听外部传入的进度变化
watch(() => props.progress, (newProgress) => {
  currentProgress.value = newProgress;
}, { immediate: true });

const handleBack = () => {
  emit('back');
};
</script>

<style lang="scss" scoped>
.icon-fanhui {
  position: fixed;
  left: 10px;
  top: 10px;
  z-index: 20;
}

.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 1);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  z-index: 10000;
  transition: opacity 0.3s ease;

  .circle-wrap {
    margin-top: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}
</style>
