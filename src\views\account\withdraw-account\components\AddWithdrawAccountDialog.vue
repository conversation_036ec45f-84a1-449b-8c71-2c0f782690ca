<!-- 添加银行卡账号的前置弹窗 -->
<template>
  <ZActionSheet
    v-model="showAddFundDialog"
    :title="'Add Fund Account'"
    :showCancelButton="false"
    :onConfirm="handleComfirm"
    :confirmDisabled="dialogStepInfo.confirmDisabled"
  >
    <div class="dialog-content">
      <div class="send-code-tip" v-show="hasSentCode">
        A text message with a 6-digit code was just sent to
        <b>{{ formattedUserPhone }}</b>
      </div>
      <div class="phone-input-code">
        <label for="verCode">Enter a verification code</label>
        <div class="phone-input-container">
          <input
            v-model="verificationCode"
            maxlength="6"
            type="text"
            placeholder="Enter the code"
          />
          <ZButton class="get-code-btn" :disabled="isCounting" @click="handleGetCode">
            {{ isCounting ? `${count}s` : "Get Code" }}
          </ZButton>
        </div>
      </div>
    </div>
  </ZActionSheet>
</template>

<script setup lang="ts">
import { sendCodeMsg, verifyCode } from "@/api/user";
import { GeetestMgr, GEETEST_TYPE } from "@/utils/GeetestMgr";
import { useGlobalStore } from "@/stores/global";
import { ref, computed } from "vue";
import { showToast } from "vant";
import { useWithdrawStore } from "@/stores/withdraw";
const withdrawStore = useWithdrawStore();
const { showAddFundDialog } = storeToRefs(withdrawStore);

const phone = ref("");
const verificationCode = ref(""); // 验证码
const hasSentCode = ref(false); // 是否已发送验证码
const isCounting = ref(false); // 是否正在倒计时
const count = ref(60); // 倒计时数值
let countdownTimer: NodeJS.Timeout | null = null; // 倒计时定时器

// 全局状态
const globalStore = useGlobalStore();
const userInfo = ref(globalStore.userInfo);

const formattedUserPhone = computed(() => {
  const phone = userInfo.value.phone || "";
  if (phone) return phone.slice(0, 2) + "****" + phone.slice(6, 10);
  return "";
});

const dialogStepInfo = computed(() => {
  return {
    title: "Add Fund Account",
    confirmText: "Confirm",
    confirmDisabled: !verificationCode.value,
  };
});

/**
 * 通用极验校验函数
 * @param loginType 极验类型
 * @param callback  校验成功后的回调
 */
const geetestValidate = (loginType: string, callback: Function) => {
  if (!loginType) return;

  GeetestMgr.instance.geetest_device(loginType, (successRes) => {
    if (successRes) {
      callback({
        geetest_guard: successRes.geetest_guard || "",
        userInfo: successRes.userInfo || "",
        geetest_captcha: successRes.geetest_captcha || "",
        buds: successRes.buds || "64",
      });
    }
  });
};

const handleComfirm = () => {
  if (verificationCode.value.length !== 6) {
    showToast("Please enter the correct verification code");
    return;
  }
  verifyCode({
    phone: userInfo.value.phone,
    code: verificationCode.value,
    type: userInfo.value.phone ? "15" : "12",
  })
    .then(() => {
      // todo
    })
    .catch(console.error);
};

/**
 * 发送验证码
 * 逻辑：极验校验 → 调用发送接口 → 启动倒计时
 */
const handleGetCode = async () => {
  if (isCounting.value) return; // 防止重复发送
  let loginType = userInfo.value.phone
    ? GEETEST_TYPE.change_pt_phone_code
    : GEETEST_TYPE.bind_pt_phone_code;
  let geeType = userInfo.value.phone ? "15" : "12";
  geetestValidate(loginType, async (geetestData) => {
    const params = {
      phone: userInfo.value.phone || "",
      telephoneCode: "+63",
      type: geeType,
      ...geetestData,
    };
    await sendCodeMsg(params);
    hasSentCode.value = true;
    startCountdown(); // 启动倒计时
  });
};

// 重置
watch(showAddFundDialog, (newValue) => {
  if (!newValue) {
    phone.value = "";
    verificationCode.value = "";
    hasSentCode.value = false;
    isCounting.value = false;
    count.value = 60;
  }
});

/**
 * 启动倒计时
 * 逻辑：60秒倒计时，结束后清除定时器
 */
const startCountdown = () => {
  if (isCounting.value) return;
  isCounting.value = true;
  count.value = 60;
  countdownTimer = setInterval(() => {
    count.value--;
    if (count.value <= 0) {
      clearInterval(countdownTimer);
      isCounting.value = false;
    }
  }, 1000);
};
</script>

<style scoped lang="scss">
.dialog-content {
  // 验证码步骤样式
  .send-code-tip {
    color: #222;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 16px;

    /* 171.429% */
  }

  .phone-input-code {
    label {
      color: #666;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      margin-bottom: 6px;
      display: inline-block;
    }

    .phone-input-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 16px;

      input {
        flex: 1;
        height: 40px;
        border: 1px solid #eee;
        border-radius: 20px;
        padding: 0 10px;
        outline: none;
        background-color: #f4f7fd;
      }

      .get-code-btn {
        width: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        // padding: 12px 16px;
        height: 40px;
        line-height: 40px;
        border-radius: 20px;
        background: #ac1140;
        color: #fff;
        text-align: center;
        font-size: 14px;
        cursor: pointer;

        &.is-counting {
          background: rgba(172, 17, 64, 0.5);
          cursor: not-allowed;
        }
      }
    }
  }
}

.phone-input {
  p {
    color: #666;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 8px;
  }

  input {
    flex: 1;
    height: 40px;
    border: 1px solid #eee;
    border-radius: 20px;
    padding: 0 10px;
    outline: none;
    background-color: #f4f7fd;
    width: 100%;
  }
}
</style>
