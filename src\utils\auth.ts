import { useGlobalStore, GLOBAL_STORE } from "@/stores/global";
import { getLocalStorage, setLocalStorage, removeLocalStorage } from "@/utils/core/Storage";

// 获取 token
export const getToken = (): string => {
  try {
    const store = getLocalStorage(GLOBAL_STORE);
    if (!store) return "";
    return store?.token || "";
  } catch {
    return "";
  }
};

// 移除 token
export const removeToken = (): void => {
  const globalStore = useGlobalStore();
  globalStore.loginOut();
};
