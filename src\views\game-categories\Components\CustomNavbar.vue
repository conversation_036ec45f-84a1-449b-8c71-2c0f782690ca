<script setup lang="ts">
import { ref, computed, watch, onMounted, onActivated } from "vue";
import { useRouter, useRoute } from "vue-router";
import { debounce } from "lodash-es";
import type { CustomNavbarProps, CustomNavbarEmits } from "../types";
import { useGameFiltersStore } from "@/stores/gameFilters";
import ProviderFilter from "@/components/ProviderFilter.vue";

// Props 和 Emits
const props = defineProps<CustomNavbarProps>();
const emit = defineEmits<CustomNavbarEmits>();

// 响应式状态
const visible = defineModel("visible", {
  type: Boolean,
  default: false,
});

const router = useRouter();
const route = useRoute();

// 使用全局筛选状态
const gameFiltersStore = useGameFiltersStore();

// 本地状态（用于双向绑定）
const searchValue = ref("");
const checkedProviders = ref(["all"]);

// 计算属性
const hasSearchValue = computed(() => searchValue.value.trim().length > 0);

// 从全局状态同步到本地状态
const syncFromSharedState = () => {
  searchValue.value = gameFiltersStore.filterState.searchValue;
  checkedProviders.value = [...gameFiltersStore.filterState.selectedCategories];
};

// 监听全局筛选状态变化，实时同步到本地状态
watch(
  () => gameFiltersStore.filterState,
  (newState) => {
    searchValue.value = newState.searchValue;
    checkedProviders.value = [...newState.selectedCategories];
  },
  { deep: true, immediate: true }
);

// 监听本地搜索值变化，立即同步到共享状态
watch(
  () => searchValue.value,
  (newValue) => {
    // 当搜索值变为空时，立即同步
    if (!newValue || !newValue.trim()) {
      gameFiltersStore.setSearchValue("", router, route);
    }
  }
);

// 实时搜索 - 修复：无论是否有值都要触发搜索
const handleSearchInput = () => {
  handleSearch();
};

const handleSearch = debounce(() => {
  const trimmedValue = searchValue.value.trim();
  gameFiltersStore.setSearchValue(trimmedValue, router, route);
  emit("search", trimmedValue);
}, 300);

const handleClearSearch = () => {
  searchValue.value = "";
  gameFiltersStore.setSearchValue("", router, route);
  emit("search", "");
};

const handleGoBack = () => {
  if (window.history.length > 1) {
    router.back();
  } else {
    router.push("/");
  }
};

const handleConfirmFilters = (selectedProviders: string[], providerDetails: any[]) => {
  checkedProviders.value = selectedProviders;
  gameFiltersStore.setSelectedCategories(selectedProviders, router, route);
  visible.value = false;
  props.confirm(selectedProviders, providerDetails);
};

const hasFilters = computed(() => {
  return checkedProviders.value.some((v) => v !== "all");
});

// 初始化时从共享状态同步
onMounted(() => {
  syncFromSharedState();
});

// keep-alive 环境下，每次激活都同步状态
onActivated(() => {
  // 立即同步
  syncFromSharedState();
});

// 强制刷新状态的方法
const forceRefresh = () => {
  syncFromSharedState();
};

defineExpose({
  setSearchValue: (val: string) => {
    searchValue.value = val || "";
  },
  setCheckedProviders: (val: Array<string>) => {
    checkedProviders.value = val || ["all"];
  },
  syncFromSharedState,
  forceRefresh,
});
</script>

<template>
  <div class="nav-bar">
    <!-- 返回按钮 -->
    <ZIcon type="icon-fanhui1" @click="handleGoBack" color="" :size="28"></ZIcon>
    <!-- 搜索框 -->
    <van-search
      left-icon=""
      shape="round"
      background="#f4f7fd"
      v-model="searchValue"
      placeholder="Search Games"
      @clear="handleClearSearch"
      @input="handleSearchInput"
    >
      <template #right-icon v-if="!hasSearchValue">
        <ZIcon type="icon-search" @click="handleSearch" color=""></ZIcon>
      </template>
    </van-search>
    <!-- 筛选按钮 -->
    <div class="categories-btn">
      <ZIcon
        type="icon-shaixuan"
        @click.stop="() => (visible = true)"
        :color="hasFilters ? 'red' : ''"
      ></ZIcon>

      <ProviderFilter
        v-model:visible="visible"
        :confirm="handleConfirmFilters"
        :checkedProviders="checkedProviders"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #fff;

  &:deep(.van-search) {
    flex: 1;
    height: 44px;
    margin: 0px 12px;
    border-radius: 999px;

    .van-search__content {
      background-color: #f4f7fd;
      transition: all 0.3s ease;
      border: 1px solid transparent;

      &:focus-within {
        // border-color: var(--primary-color, #007bff);
        // box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
      }
    }

    .van-field__control {
      font-size: 14px;
      color: #333;
    }

    .van-field__control::placeholder {
      color: #999;
    }
  }
}
</style>
