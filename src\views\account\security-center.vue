<script setup lang="ts">
import { ALL_APP_SOURCE_CONFIG } from "@/utils/config/Config";
import LineSetting from "@/views/account/components/LineSetting.vue";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";
import { KycMgr, InGameType, KycState } from "@/utils/KycMgr";
import { formatPhoneNumber } from "@/utils/core/tools";
import { PN_VERIFY_TYPE } from "@/components/ZVerifyDialog/types";
import { logout, getUserKyc } from "@/api/user";
import { ref } from "vue";
import { useRouter } from "vue-router";
import { storeToRefs } from "pinia";
import { useGlobalStore } from "@/stores/global";
import { showToast } from "vant";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { AutoPopMgr } from "@/utils/AutoPopMgr";

const autoPopMgrStore = useAutoPopMgrStore();

const globalStore = useGlobalStore();
const router = useRouter();
const { userInfo } = storeToRefs(globalStore);

const showUpdatePhoneDialog = ref(false);
const showBindPhoneDialog = ref(false);

const showInitLoginPasswordDialog = ref(false);
const showUpdateLoginPasswordDialog = ref(false);

const showInitPaymentPasswordDialog = ref(false);
const showUpdatePaymentPasswordDialog = ref(false);

const showLogoutDialog = ref(false);
// 存储修改后的信息

const kycDetail = ref({});
const kycData = ref({});

const init = async () => {
  getKycState();
  getKycData();
};

const getKycData = async () => {
  const res = await getUserKyc({});
  kycData.value = res;
};

const getKycState = async () => {
  KycMgr.instance.clearData();
  const res = await KycMgr.instance.verifyKyc(InGameType.UNKNOWN);
  kycDetail.value = res;
  //res.state: 状态0未认证 1已认证 2审核中 3拒绝 -1未读服务器
  //res.is_full: 是否是简版本 0是简版 1是完整版 -1未读服务器
};

const jumpKycDetail = () => {
  router.push(`/kyc/detail`);
};
const jumpKycForm = () => {
  // KycMgr.instance.checkBindPhone()
  if (KycMgr.instance.kycSimple === 0) {
    // 简版
    router.push(`/kyc/simple-form`);
  } else {
    // 详版
    router.push(`/kyc/normal-form`);
  }
};
// kyc跳转
const handleKycBtn = () => {
  if (globalStore.channel === CHANEL_TYPE.MAYA) {
    jumpKycDetail();
  } else if (globalStore.channel === CHANEL_TYPE.G_CASH) {
    let first_restriction = kycData.value.first_restriction;
    if (!first_restriction) {
      jumpKycForm();
    } else {
      jumpKycDetail();
    }
  } else if (globalStore.channel === CHANEL_TYPE.WEB) {
    if (KycMgr.instance.kycState === KycState.NO_VERIFY) {
      //打开kyc开始认证
      KycMgr.instance.inGameType = InGameType.MyCenter;
      KycMgr.instance.checkBindPhone();
    } else if (
      KycMgr.instance.kycState === KycState.COMPLETE ||
      KycMgr.instance.kycState === KycState.REVIEWING
    ) {
      // 审核中、审核完成
      jumpKycDetail();
    } else if (KycMgr.instance.kycState === KycState.REJECTED) {
      // 拒绝，然后重新认证
      jumpKycForm();
    }
  }
};

// 更新/绑定手机号
const handleUpdatePhoneBtn = () => {
  // 更改手机号
  if (userInfo.value.phone) {
    showUpdatePhoneDialog.value = true;
  } else {
    // 绑定手机号
    showBindPhoneDialog.value = true;
  }
};
// 更新/设置登陆密码
const handleUpdateLoginPasswordBtn = () => {
  if (!userInfo.value.phone) {
    showToast("For safety of your account, before set login password, you must set phone number.");
    return;
  }
  if (userInfo.value.login_password) {
    showUpdateLoginPasswordDialog.value = true;
  } else {
    showInitLoginPasswordDialog.value = true;
  }
};

// 设置支付密码
const handleUpdatePaymentPasswordBtn = () => {
  if (userInfo.value.withdraw_password) {
    showUpdatePaymentPasswordDialog.value = true;
  } else {
    showInitPaymentPasswordDialog.value = true;
  }
};
// 退出登陆
const handleLogOut = async () => {
  await logout();
  // 重置弹窗状态，准备显示弹窗
  autoPopMgrStore.hasPop = false;
  AutoPopMgr.resetAllPopups();
  globalStore.loginOut();
};

//
</script>

<template>
  <ZPage
    :request="init"
    :backgroundColor="'transparent'"
    :narBarStyle="{ background: 'transparent' }"
  >
    <div v-if="globalStore.isMiniChannel">
      <div class="security-center">
        <div class="scroll-content">
          <div class="setting-title">General Setting</div>
          <div class="setting-content">
            <LineSetting
              @click="handleKycBtn"
              text="Personal Details"
              :value="
                ['Not Verified', 'Completed', 'Reviewing', 'Rejected'][kycDetail?.status] || ''
              "
            >
              <template #icon>
                <ZIcon type="icon-kyc" />
              </template>
            </LineSetting>
            <LineSetting
              text="Version No."
              :rightText="ALL_APP_SOURCE_CONFIG.app_version"
              :showArrow="false"
            >
              <template #icon>
                <ZIcon type="icon-version" />
              </template>
            </LineSetting>
          </div>
        </div>
      </div>
    </div>
    <div class="security-center" v-else>
      <div class="scroll-content">
        <div class="setting-title">General Setting</div>
        <div class="setting-content">
          <LineSetting
            @click="handleUpdatePhoneBtn"
            :value="formatPhoneNumber(userInfo.phone)"
            text="Phone"
          >
            <template #icon>
              <ZIcon type="icon-phone" />
            </template>
          </LineSetting>
          <LineSetting
            @click="handleUpdateLoginPasswordBtn"
            :value="userInfo.login_password ? 'Modify' : 'Not Set'"
            text="Login Password"
          >
            <template #icon>
              <ZIcon type="icon-loginpwd" />
            </template>
          </LineSetting>
          <LineSetting
            @click="handleUpdatePaymentPasswordBtn"
            :value="userInfo.withdraw_password ? 'Modify' : 'Not Set'"
            text="Payment Password"
          >
            <template #icon>
              <span class="iconfont icon-paypwd"></span>
            </template>
          </LineSetting>
          <LineSetting
            @click="handleKycBtn"
            text="KYC Details"
            :valueStyle="{
              color: ['#999999', '#01D46A', '#FF936F', '#AC1140'][kycDetail?.status] || '#999999',
            }"
            :value="['Not Verified', 'Completed', 'Reviewing', 'Rejected'][kycDetail?.status] || ''"
          >
            <template #icon>
              <ZIcon type="icon-kyc" />
            </template>
          </LineSetting>
        </div>
        <div class="setting-title">Account actions</div>
        <div class="setting-content">
          <LineSetting @click="showLogoutDialog = true" text="Logout">
            <template #icon>
              <ZIcon type="icon-Logout" />
            </template>
          </LineSetting>
          <LineSetting
            text="Version No."
            :rightText="ALL_APP_SOURCE_CONFIG.app_version"
            :showArrow="false"
          >
            <template #icon>
              <ZIcon type="icon-version" />
            </template>
          </LineSetting>
        </div>
      </div>
    </div>
    <!-- 更改手机号 -->
    <ZVerifyDialog
      v-model:showDialog="showUpdatePhoneDialog"
      :verifyType="PN_VERIFY_TYPE.ChangePhoneNumber"
      :succCallBack="() => (showUpdatePhoneDialog = false)"
    />

    <!-- 绑定手机号 -->
    <VerifyDialogChangePhone
      v-model:showDialog="showBindPhoneDialog"
      :verifyType="PN_VERIFY_TYPE.SetPhoneNumber"
      :succCallBack="() => (showBindPhoneDialog = false)"
    />

    <!-- 更新登陆密码 -->
    <ZVerifyDialog
      v-model:showDialog="showUpdateLoginPasswordDialog"
      :verifyType="PN_VERIFY_TYPE.ForgetPassword"
      :succCallBack="() => (showUpdateLoginPasswordDialog = false)"
    />
    <!-- 首次设置登陆密码 -->
    <VerifyDialogLoginPassword
      v-model:showNextDialog="showInitLoginPasswordDialog"
      :succCallBack="() => (showInitLoginPasswordDialog = false)"
    >
    </VerifyDialogLoginPassword>

    <!--更新支付密码 -->
    <ZVerifyDialog
      v-model:showDialog="showUpdatePaymentPasswordDialog"
      :verifyType="PN_VERIFY_TYPE.ChangePaymentPassword"
      :succCallBack="
        () => {
          showUpdatePaymentPasswordDialog = false;
        }
      "
    >
    </ZVerifyDialog>
    <!-- 首次支付密码 -->
    <VerifyDialogPaymentPassword
      v-model:showNextDialog="showInitPaymentPasswordDialog"
      :verifyType="PN_VERIFY_TYPE.SetPaymentPassword"
      @complete="showInitPaymentPasswordDialog = false"
    >
    </VerifyDialogPaymentPassword>

    <!-- 退出登陆 -->
    <ZActionSheet
      v-model="showLogoutDialog"
      title="Tips"
      :showCancelButton="false"
      confirmText="Log Out"
      :onConfirm="handleLogOut"
    >
      Confirm to log out?
    </ZActionSheet>
  </ZPage>
</template>

<style scoped lang="scss">
.security-center {
  height: 100%;
  background-color: #f4f8fb;

  .icon-version {
    display: inline-block;
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    background-color: #97acff1a;
  }

  .scroll-content {
    color: rgba(48, 48, 48, 1);
    // --优化 高度
    overflow-y: auto;

    .setting-title {
      font-size: 14px;
      color: #999;
      padding: 10px 0 6px 10px;
      color: #999;
      font-family: Inter;
    }

    .setting-content {
      background-color: #fff;
      margin: 10px 16px;
      box-sizing: border-box;
      padding: 8px;
      border-radius: 10px;

      .iconfont {
        font-size: 20px;
      }
    }
  }
}
</style>
