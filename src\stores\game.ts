import { defineStore } from "pinia";
import { orderBy } from "lodash-es";
import { getGameConfig, getGames, getUpdateList, likeHistory } from "@/api/games";
import { gameTypes as GameTypes } from "@/utils/GameMenu";
import { getServerSideImageUrl } from "@/utils/core/tools";
import type { Game, Category, Provider, GameAllSet, LiveGame, GameStoreState } from "@/types/store";
import type { GameConfigApiResponse, UpdateListApiResponse, LoginConfigData } from "@/types/api";
import { setLocalStorage } from "@/utils/core/Storage";
import { imagePreloader, type PreloadOptions } from "@/utils/core/imagePreloader";
import { useGlobalStore } from "@/stores/global";
import router from "@/router";
import { nextTick } from "vue";
import { MAINTENANCETIPCODE } from "@/utils/config/GlobalConstant";
import { getGlobalDialog } from "@/enter/vant";

export const GAME_STORE = "GAME_STORE";

let sortFunc = (a, b) => {
  if (parseInt(a.tags) === 0 && parseInt(b.tags) !== 0) return 1;
  if (parseInt(a.tags) !== 0 && parseInt(b.tags) === 0) return -1;
  //排序tags 1>3>2
  const aint = parseInt(a.tags);
  const bint = parseInt(b.tags);
  if (aint !== bint) {
    if (aint == 1) return -1;
    if (bint == 1) return 1;
    if (aint == 3) return -1;
    if (bint == 3) return 1;
  }
  if (parseInt(a.balance_record) !== parseInt(b.balance_record))
    return parseInt(b.balance_record) - parseInt(a.balance_record);
  if (parseInt(a.sort) !== parseInt(b.sort)) return parseInt(b.sort) - parseInt(a.sort);
  if (a.name && b.name) {
    return a.name.localeCompare(b.name);
  }
};

// 维护轮询定时器
let maintenancePollingTimer: ReturnType<typeof setTimeout> | null = null;

// 维护轮询间隔（毫秒）
const MAINTENANCE_POLLING_INTERVAL = 30000;

const initValue: GameStoreState = {
  liveGame: [], // 视频数据
  gameTypes: [], // 游戏分类
  gameList: [], // 所有游戏列表
  vaildThirdCompany: [], // 有效的三方厂商，Home页底部
  allThirdCompany: [], // 所有的三方厂商，Home页底部
  maintenanceList: [], // 游戏维护ID集合
  hideList: [], // 游戏隐藏ID集合
  chooseProviders: [], // 有效的三方厂商（包含本地图片），ALL Games 页图片筛选
  providerImgs: [], // 备用的本地厂商图片
  configData: {}, // 配置所有的数据
  iosPopUpWindow: 0, //ios safari 是个需要外跳
};

export const useGameStore = defineStore("game", {
  state: (): GameStoreState => initValue,
  getters: {
    $dialog() {
      return getGlobalDialog();
    },
    /**
     * 获取分类后的游戏数据（首页专用）
     * 返回包含游戏的分类列表，每个分类的游戏已经排序并过滤隐藏游戏
     */
    homeClassifyGames(): Category[] {
      // 如果没有游戏数据，返回空的分类列表
      if (!this.gameList || this.gameList.length === 0) {
        return this.gameTypes.map((category) => ({
          ...category,
          games: [],
        }));
      }

      // 如果没有分类数据，返回空数组
      if (!this.gameTypes || this.gameTypes.length === 0) {
        return [];
      }

      // 深拷贝分类数据，避免修改原始数据
      const categoriesWithGames: Category[] = this.gameTypes.map((category) => ({
        ...category,
        games: [],
      }));

      // 将游戏分配到对应的分类中
      this.gameList.forEach((game) => {
        try {
          // 跳过隐藏的游戏
          if (this.hideList.includes(game.id)) {
            return;
          }

          // 解析游戏的分类标签
          const typeArr = JSON.parse(game.home_page_label || "[]").map((t: string) =>
            t.toUpperCase()
          );

          // 将游戏分配到匹配的分类中
          categoriesWithGames.forEach((category) => {
            if (typeArr.includes(category.game_type)) {
              category.games = category.games || [];
              category.games.push(game);
            }
          });
        } catch (error) {
          console.warn("Failed to parse home_page_label for game:", game.game_id, error);
        }
      });

      // 对每个分类中的游戏进行排序
      categoriesWithGames.forEach((category) => {
        if (category.games && category.games.length > 0) {
          category.games.sort(sortFunc);
        }
      });

      return categoriesWithGames;
    },
  },
  actions: {
    /**
     * 初始化游戏数据
     * 获取游戏配置、游戏列表、厂商列表和维护列表
     */
    async init(): Promise<void> {
      try {
        this.getGameList();
        // 获取本地厂商图片
        this.getProviderLocalImgs("home");
      } catch (error) {
        console.error("Failed to initialize game store:", error);
        throw error;
      }
    },
    /**
     * 获取维护列表和隐藏列表
     * @param polling 是否启用轮询
     */
    async getMaintenanceList(polling = true): Promise<void> {
      try {
        const res = await getUpdateList({ is_new: 1 });
        const data = res?.data || res || {};
        const { maintenance = [], hide = [] } = data;
        this.maintenanceList = maintenance;
        this.hideList = hide;
      } catch (error) {
        this.maintenanceList = [];
        this.hideList = [];
        console.error("Failed to get maintenance list:", error);
      }

      // 设置轮询
      if (polling) {
        this.clearMaintenancePolling();
        maintenancePollingTimer = setTimeout(() => {
          this.getMaintenanceList(true);
        }, MAINTENANCE_POLLING_INTERVAL);
      }
    },

    /**
     * 清除维护轮询定时器
     */
    clearMaintenancePolling(): void {
      if (maintenancePollingTimer) {
        clearTimeout(maintenancePollingTimer);
        maintenancePollingTimer = null;
      }
    },
    // 获取本地厂商图片
    getProviderLocalImgs(type = "game") {
      let imageModules = {};
      if (type === "game") {
        imageModules = import.meta.glob("@/assets/images/game/providers/*.{png,jpg}", {
          eager: true,
        });
      } else {
        imageModules = import.meta.glob("@/assets/images/home/<USER>/*.{png,jpg}", {
          eager: true,
        });
      }

      // 图片转换为可用格式
      const imageList = Object.entries(imageModules).map(([path, module]) => ({
        name: (path.split("/").pop() || "").split(".").shift() || "",
        url: (module as { default: string }).default,
      }));
      if (type === "home") {
        this.providerImgs = imageList; // 保存备用的本地厂商图片
      }
      return imageList;
    },

    // 游戏分类
    setGameTypes(game_types: Category[]) {
      // 按排序字段降序排列
      const sortedBySort = orderBy(game_types, ["sort"], ["desc"]);
      // 格式化分类数据
      const formatRes = this.formatGameConfig(sortedBySort);
      this.gameTypes = formatRes;
    },

    // 三方厂商
    setThirdCompany(third_company: Provider[]) {
      // 所有厂商,游戏跳转会用到
      this.allThirdCompany = third_company;

      //1是正常 2是维护 3是隐藏
      const vaildStatusThirdCompany = third_company
        .filter((provider: Provider) => provider.status != 3)
        .sort((a, b) => b.sort - a.sort);

      // 首页底部
      const vaildThirdCompany = vaildStatusThirdCompany.map((provider: Provider) => {
        // 作为没有在线资源的本地备选图片
        const localImg = this.providerImgs.find((img) => img.name === `p${provider.id}`)?.url || "";
        return {
          ...provider,
          imageError: !provider.icon_home && !localImg, // 如果没有图标和本地图片，则标记为错误
          url: provider.icon_home ? getServerSideImageUrl(provider.icon_home) : localImg || "",
        };
      });
      this.vaildThirdCompany = vaildThirdCompany;

      // 游戏筛选
      const localImgList = this.getProviderLocalImgs("game");
      const chooseProviders = vaildStatusThirdCompany.map((provider: Provider) => {
        const imageUrl = localImgList.find((img) => img.name === `Group_${provider.id}`)?.url || "";
        return {
          ...provider,
          imageUrl: provider.icon_other
            ? getServerSideImageUrl(provider.icon_other)
            : imageUrl || "",
          imageError: !provider.icon_other && !imageUrl,
        };
      });

      this.chooseProviders = chooseProviders;
      // 预加载图片
      const images = this.chooseProviders
        .map((item) => item.imageUrl)
        .filter((url): url is string => typeof url === "string" && url !== "");
      imagePreloader.preloadList(images, {});
    },

    setLoginWay(login_conf) {
      const globalStore = useGlobalStore();
      globalStore.loginConfig = login_conf;
    },

    setLiveGame(live_game: LiveGame[] = []) {
      this.liveGame = live_game.sort((a, b) => (b.sort || 0) - (a.sort || 0));
    },

    /**
     * 获取游戏配置
     * @returns 格式化后的游戏分类列表
     */
    async getConfigData(): Promise<void> {
      try {
        const response = (await getGameConfig()) as any;

        // 检查是否有有效数据
        if (response && response.code === 200) {
          const data = response.data as unknown as GameConfigApiResponse;
          // 保存原始配置数据
          this.configData = data;

          // 批量设置配置数据
          this.setGameTypes(data.game_type || []);
          this.setThirdCompany(data.third_company || []);
          this.setLiveGame(data.live_game || []);
          this.setLoginWay(data.login_conf || ({} as LoginConfigData));
          this.iosPopUpWindow = Number(data.ios_pop_up_window);

          // 存储单次充值最大值配置
          if (data.maximum_single_recharge) {
            setLocalStorage("maximum_single_recharge", data.maximum_single_recharge);
          }
        } else if (response.code === MAINTENANCETIPCODE) {
          // 系统维护处理
          router.replace("/system/maintenance");
        } else {
          // 网络连接错误处理
          this.showNetworkErrorDialog();
        }
      } catch (error) {
        this.setGameTypes(GameTypes || []);
        this.showNetworkErrorDialog();
      }
    },

    /**
     * 显示网络错误对话框并提供重试选项
     */
    async showNetworkErrorDialog(): Promise<void> {
      try {
        this.$dialog({
          message: "Could not connect to network, please try again.",
          showCancelButton: false,
          onConfirm: () => {
            this.getConfigData();
          },
        });
      } catch (dialogError) {
        console.error("显示网络错误对话框失败:", dialogError);
      }
    },

    /**
     * 获取游戏列表
     */
    async getGameList(): Promise<Game[]> {
      try {
        const response = await getGames({ is_new: 1 });
        const gameList = response as unknown as Game[];
        this.gameList = gameList;
        return gameList;
      } catch (error) {
        console.error("获取游戏列表失败:", error);
        this.gameList = [];
        return [];
      }
    },
    /**
     * 格式化游戏配置
     * @param gameTypes 原始游戏分类数据
     * @returns 格式化后的游戏分类列表
     */
    formatGameConfig(gameTypes: any[]): Category[] {
      // 按排序字段降序排列
      const sortedBySort = orderBy(gameTypes, ["sort"], ["desc"]);

      // 格式化分类数据
      return sortedBySort.map(({ game_type, id, sort, ...rest }) => {
        const name = this.formatCategoryName(game_type);
        return {
          ...rest,
          game_type,
          id,
          sort,
          name,
          games: [], // 初始化游戏数组
        };
      });
    },

    /**
     * 格式化分类名称
     * @param gameType 游戏类型
     * @returns 格式化后的名称
     */
    formatCategoryName(gameType: string): string {
      if (!gameType || typeof gameType !== "string") {
        return "";
      }
      return gameType.charAt(0).toUpperCase() + gameType.slice(1).toLowerCase();
    },

    /**
     * 清空 store 数据
     */
    clearStore(): void {
      Object.assign(this, { ...initValue });
      // 清除轮询定时器
      this.clearMaintenancePolling();
    },

    /**
     * 锦标赛排行榜 BetNow跳转逻辑
     * @param options 跳转选项
     * @param options.vendors 厂商列表
     * @param options.game_types 游戏类型列表
     */
    async handleBetNowNavigation(options: {
      vendors: string[];
      game_types: string[];
    }): Promise<void> {
      const { vendors, game_types } = options;

      // 检查厂商和游戏类型是否与当前状态匹配
      const vendorsMatch = this.checkVendorsMatch(vendors);
      const gameTypesMatch = this.checkGameTypesMatch(game_types);
      console.log("锦标赛排行榜 BetNow跳转逻辑", vendorsMatch, gameTypesMatch);
      if (vendorsMatch && gameTypesMatch) {
        // 情况1: 厂商和游戏类型都匹配，跳转到首页并滚动到页面第一个游戏类型的第一排游戏
        this.navigateToHomeWithGameType();
      } else if (vendorsMatch && !gameTypesMatch) {
        // 情况2: 厂商匹配但游戏类型不匹配，跳转到首页并滚动到第一个游戏类型的第一排游戏
        this.navigateToHomeWithGameType(game_types[0]);
      } else {
        // 情况3和4: 厂商不匹配，跳转到游戏分类页面
        // 先获取游戏数据
        // await this.getConfigAndGamesData();
        await this.getGameList();
        console.log("后对比各分类下筛选厂商信息", game_types, vendors);
        // 后对比各分类下筛选厂商信息
        this.navigateToGameCategories(game_types, vendors);
      }
    },

    /**
     * 检查厂商是否匹配
     * @param vendors 厂商列表
     * @returns 是否匹配
     */
    checkVendorsMatch(vendors: string[]): boolean {
      if (vendors.length < 1) return false;
      if (vendors.length !== this.allThirdCompany.length) return false;
      // 检查传入的厂商是否为完整的allThirdCompany
      return vendors.every((vendor) =>
        this.allThirdCompany.some(
          (company) =>
            (company.provider && company.provider.toLowerCase() === vendor.toLowerCase()) ||
            (company.id && String(company.id) === vendor)
        )
      );
    },

    /**
     * 检查游戏类型是否匹配
     * @param gameTypes 游戏类型列表
     * @returns 是否匹配
     */
    checkGameTypesMatch(gameTypes: string[]): boolean {
      if (gameTypes.length < 1) return false;
      if (gameTypes.length !== this.gameTypes.length) return false;
      // 检查传入的游戏类型是否都在gameTypes中
      return gameTypes.every((type) =>
        this.gameTypes.some(
          (category) =>
            category.name.toLowerCase() === type.toLowerCase() ||
            String(category.id).toLowerCase() === type.toLowerCase()
        )
      );
    },

    /**
     * 跳转到首页并滚动到指定游戏类型
     * @param gameType 游戏类型
     */
    navigateToHomeWithGameType(gameType?: string): void {
      router.push("/home").then(() => {
        nextTick(() => {
          // 找到对应的游戏类型索引
          let index = 0;
          if (gameType)
            index = this.gameTypes.findIndex(
              (category) =>
                category.name.toLowerCase() === gameType.toLowerCase() ||
                String(category.id).toLowerCase() === gameType.toLowerCase()
            );

          if (index !== -1) {
            // 滚动到对应的游戏类型
            const section = document.getElementById(`category-${index}`);
            if (section) {
              const mainContent = document.querySelector(".main-content");
              if (mainContent) {
                const offset = section.offsetTop - 100; // 减去顶部导航高度
                mainContent.scrollTo({
                  top: offset,
                  behavior: "smooth",
                });
              }
            }
          }
        });
      });
    },

    /**
     * 跳转到游戏分类页面
     * @param gameTypes 游戏类型列表
     * @param vendors 厂商列表
     */
    navigateToGameCategories(gameTypes: string[], vendors: string[]): void {
      // 构建查询参数
      const query: any = {};
      if (gameTypes.length > 0) {
        // 找到第一个匹配的游戏类型ID
        let firstGameType: Category | undefined = undefined;
        for (const type of gameTypes) {
          firstGameType = this.gameTypes.find((category) => {
            return (
              String(category.id) === type &&
              category.games &&
              category.games?.length > 0 &&
              category.games.some((game) => vendors.includes(`${game.company_id}`))
            );
          });
          if (firstGameType) {
            break; // 找到后停止查找
          }
        }
        if (firstGameType && firstGameType.id) {
          query.categoryId = firstGameType.id;
          query.providerIds = vendors.join(",");
        }
      }
      // 跳转到游戏分类页面
      router
        .push({
          path: "/game-categories",
          query,
        })
        .then(() => {
          // 延迟设置筛选条件，确保页面已加载
          setTimeout(() => {
            // 通过事件或全局状态来设置筛选条件
            const event = new CustomEvent("setGameFilters", {
              detail: {
                providers: vendors.length > 0 ? vendors : ["all"],
                gameTypes: gameTypes,
              },
            });
            window.dispatchEvent(event);
          }, 500);
        });
    },

    /**
     * 查找第一个有内容的游戏类型并切换到该类型
     * @param gameTypes 游戏类型列表
     */
    findFirstAvailableGameType(gameTypes: string[]): void {
      // 通过事件通知游戏分类页面切换到第一个有内容的游戏类型
      setTimeout(() => {
        const event = new CustomEvent("switchToFirstAvailableGameType", {
          detail: { gameTypes },
        });
        window.dispatchEvent(event);
      }, 800);
    },

    // Bet Now 跳转
    // 如果 History 页面无数据，跳转至 Like 页，若 Like 页也无数据。跳转至首页 Top Game 的位置
    async clickBetNow(callBack?: () => void) {
      try {
        const likeResponse = await likeHistory({ type: 1 });
        const historyResponse = await likeHistory({ type: 2 });
        const likeList = likeResponse.data || likeResponse;
        const historyList = historyResponse.data || historyResponse;
        callBack && callBack();
        if (historyList?.length) {
          router.push(`/game-categories?categoryId=history`);
        } else if (likeList?.length) {
          router.push(`/game-categories?categoryId=like`);
        } else {
          router.push("/home");
        }
      } catch (error) {
        console.error("Failed to load game history:", error);
        callBack && callBack();
        router.push("/home");
      }
    },
  },
  persist: {
    key: GAME_STORE,
    storage: window.localStorage,
  },
});
