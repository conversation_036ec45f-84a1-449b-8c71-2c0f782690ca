<template>
  <div class="line-item" @click="$emit('click')">
    <div class="left">
      <div class="icon">
        <slot name="icon"></slot>
      </div>
      <span class="text">{{ text }}</span>
    </div>
    <div class="arrow">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z" fill="currentColor" />
      </svg>
    </div>
  </div>
</template>

<script setup>
defineProps({
  text: {
    type: String,
    required: true,
  },
});

defineEmits(["click"]);
</script>

<style lang="scss" scoped>
.line-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  cursor: pointer;
  height: 60px;
  box-sizing: border-box;

  .left {
    display: flex;
    align-items: center;
    gap: 12px;

    .icon {
      display: flex;
      align-items: center;
      color: #333;
    }

    .text {
      font-size: 16px;
      color: #333;
      font-weight: 500;
    }
  }

  .arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
  }

  &:active {
    background: rgba(0, 0, 0, 0.05);
  }
}
</style>
