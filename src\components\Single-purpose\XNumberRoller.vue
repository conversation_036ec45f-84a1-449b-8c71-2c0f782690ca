<template>
  <!-- 数字滚动组件 -->
  <span class="rolling-number" :style="{
    fontSize: fontSize,
    color: textColor,
    fontWeight: fontWeight,
  }">
    <span v-for="(item, idx) in formattedDigits" :key="`digit-${idx}`">
      <template v-if="typeof item === 'number'">
        <span class="digit-container">
          <span class="digit-list" :style="getDigitStyle(digitIndex(idx))">
            <!-- 目的：9变1时会向上滚动到11（9→10→1），实现视觉上的“向上滚动” -->
            <span v-for="n in 20" :key="n" class="digit">
              {{ (n - 1) % 10 }}
            </span>
          </span>
        </span>
      </template>
      <template v-else>
        <span class="separator">{{ item }}</span>
      </template>
    </span>
  </span>
</template>

<script setup>
import { ref, watch, computed } from "vue";

const props = defineProps({
  // 要显示的数值
  value: {
    type: Number,
    required: true,
  },
  // 动画持续时间（毫秒）
  duration: {
    type: Number,
    default: 800,
    validator: (value) => value > 0,
  },
  // 字体大小
  fontSize: {
    type: String,
    default: "14px",
  },
  // 文字颜色
  textColor: {
    type: String,
    default: "#333",
  },
  // 字体粗细
  fontWeight: {
    type: [String, Number],
    default: "bold",
  },
  // 是否启用千分位分隔符
  useThousandSeparator: {
    type: Boolean,
    default: true,
  },
});

// 响应式数据
const prevValue = ref(Number(props.value));
const displayDigits = ref([]);
const rollingIndexes = ref([]);
const rollingTo9To0 = ref([]);

/**
 * 将数字转换为数字数组（不包含分隔符）
 * @param {number} num - 要转换的数字
 * @returns {number[]} 数字数组
 */
function getDigits(num) {
  return num
    .toString()
    .split("")
    .map((d) => Number(d));
}

/**
 * 获取当前显示的数字数组
 * @returns {number[]} 当前数字数组
 */
function getDisplayDigits() {
  return getDigits(props.value);
}

// 生成带千分位分隔符的数组
const formattedDigits = computed(() => {
  if (!props.useThousandSeparator) {
    return getDigits(props.value);
  }

  const str = props.value.toLocaleString();
  return str.split("").map((d) => (/\d/.test(d) ? Number(d) : d));
});

/**
 * 计算当前数字位在纯数字数组中的索引
 * @param {number} idx - 在格式化数组中的索引
 * @returns {number} 在纯数字数组中的索引
 */
function digitIndex(idx) {
  // 统计前面有多少个数字
  return formattedDigits.value.slice(0, idx).filter((d) => typeof d === "number").length;
}

// 监听数值变化
watch(
  () => Number(props.value),
  (newVal, oldVal) => {
    if (newVal === oldVal) return;
    prevValue.value = oldVal;
    updateRollingIndexes();
    animate();
  },
  { immediate: false }
);

/**
 * 更新滚动索引和方向
 */
function updateRollingIndexes() {
  const oldDigits = getDigits(prevValue.value);
  const newDigits = getDigits(props.value);

  // 补齐位数，确保两个数组长度相同
  const maxLength = Math.max(oldDigits.length, newDigits.length);
  while (oldDigits.length < maxLength) oldDigits.unshift(0);
  while (newDigits.length < maxLength) newDigits.unshift(0);

  // 计算哪些位需要滚动
  rollingIndexes.value = newDigits.map((digit, index) => digit !== oldDigits[index]);

  // 计算是否需要从9滚动到0（向上滚动）
  rollingTo9To0.value = newDigits.map((digit, index) => {
    return digit < oldDigits[index];
  });

  displayDigits.value = newDigits;
}

/**
 * 执行动画
 */
function animate() {
  displayDigits.value = getDisplayDigits();
}

/**
 * 获取数字位的样式
 * @param {number} idx - 数字位索引
 * @returns {object} 样式对象
 */
const getDigitStyle = (idx) => {
  const digit = displayDigits.value[idx];
  if (typeof digit !== "number") return {};

  const needRoll = rollingIndexes.value[idx];
  const isRollOver = rollingTo9To0.value[idx];
  const digitHeight = 1.2; // em

  // 计算变换距离
  let translateY;
  if (needRoll && isRollOver) {
    // 从大数字滚动到小数字（如9→1），需要额外滚动10位
    translateY = (10 + digit) * digitHeight;
  } else {
    // 正常滚动或无滚动
    translateY = digit * digitHeight;
  }

  return {
    transform: `translateY(-${translateY}em)`,
    transition: needRoll
      ? `transform ${props.duration}ms cubic-bezier(0.23, 1.02, 0.67, 1)`
      : "none",
  };
};

// 初始化
displayDigits.value = getDisplayDigits();
rollingIndexes.value = displayDigits.value.map(() => false);
rollingTo9To0.value = displayDigits.value.map(() => false);
</script>

<style scoped>
.rolling-number {
  display: inline-flex;
  align-items: center;
  letter-spacing: 0.5px;
  font-family: "D-DIN", "Inter";
  user-select: none;
  margin-top: 4px;
}

.digit-container {
  display: inline-block;
  height: 1.2em;
  overflow: hidden;
  position: relative;
  text-align: center;
  vertical-align: top;
}

.digit-list {
  display: flex;
  flex-direction: column;
  will-change: transform;
}

.digit {
  height: 1.2em;
  line-height: 1.2em;
  display: flex;
  align-items: center;
  justify-content: center;
  font-variant-numeric: tabular-nums;
}

.separator {
  display: inline-block;
  text-align: center;
  height: 1.2em;
  line-height: 1.2em;
  font-variant-numeric: tabular-nums;
}

/* 性能优化 */
.digit-container,
.digit-list {
  transform: translateZ(0);
  backface-visibility: hidden;
}
</style>
