<template>
  <div class="header-filter">
    <ZSelect
      :modelValue="selectedDateOption"
      :selectList="dateOptions"
      @confirm="handleDateConfirm"
      title="Select a Date"
      :fieldNames="{ label: 'name', value: 'value' }"
    ></ZSelect>
    <button @click.stop="() => (providerVisible = true)">
      {{ getProviderDisplayText }}
      <span class="provider-len" v-show="filterProvider[0]?.provider">
        +{{ filterProvider.length }}
      </span>
      <span class="iconfont icon-xiangxia"></span>
    </button>

    <ProviderFilter
      v-model:visible="providerVisible"
      :confirm="handleProviderConfirm"
      :checkedProviders="filterProvider.map((p) => String(p.id))"
    />
  </div>
</template>

<script setup lang="ts">
import ProviderFilter from "@/components/ProviderFilter.vue";
import { ref, computed } from "vue";

interface DateOption {
  name: string;
  value: number;
}

interface FilterProvider {
  id: string | number;
  provider: string;
}

interface Props {
  activeBtn: DateOption;
  filterProvider: FilterProvider[];
  dateOptions: DateOption[];
}

interface Emits {
  (e: "update:activeBtn", value: DateOption): void;
  (e: "update:filterProvider", value: FilterProvider[]): void;
  (e: "filter-change"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 不再需要 gameStore，因为 providerDetails 已经从组件传入

// Local state
const dateVisible = ref(false);
const providerVisible = ref(false);
const selectedDateOption = ref(props.activeBtn.value);

// Computed properties
const getProviderDisplayText = computed(() => {
  return props.filterProvider[0]?.provider || "Provider";
});

// Date filter handlers
const openDateFilter = () => {
  selectedDateOption.value = props.activeBtn.value;
  dateVisible.value = true;
};

const handleDateConfirm = (e) => {
  console.log("handleDateConfirm", e);
  if (e) {
    emit("update:activeBtn", e);
    emit("filter-change");
  }
  dateVisible.value = false;
};

const handleDateCancel = () => {
  dateVisible.value = false;
};

// Provider filter handlers
const handleProviderConfirm = (selectedProviders: string[], providerDetails: any[]) => {
  // 直接使用传入的 providerDetails，过滤掉 "all"
  const filteredProviders = providerDetails.filter((detail) => detail.id !== "all");

  emit("update:filterProvider", filteredProviders);
  emit("filter-change");
};
</script>

<style lang="scss" scoped>
.header-filter {
  overflow: hidden;
  display: flex;
  gap: 8px;
  padding: 0 12px;
  top: 55px;
  left: 0;
  position: absolute;
  font-family: "Inter";
  z-index: 10;

  &:deep(.select) {
    height: 28px;
    padding: 0 28px 0 14px;
    background: #fff;
    font-weight: 500;
    font-family: "Inter";
    color: #666;
    font-size: 14px;
    .select-icon {
      color: #666 !important;
    }
  }

  button {
    color: #666666;
    font-size: 14px;
    font-weight: 500;
    height: 28px;
    padding: 0 14px;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    border-radius: 30px;

    > span {
      padding-left: 10px;
    }

    .provider-len {
      padding: 0 8px;
      color: #fff;
      display: inline-block;
      background-color: #ac1140;
      border-radius: 999px;
      text-align: center;
    }

    img {
      margin-left: 10px;
      vertical-align: sub;
      width: 16px;
      height: 16px;
    }
  }
}

.content {
  width: 100%;

  &:deep(.van-cell-group) {
    margin: 0;
  }

  .check-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid #d0d0d0;
    border-radius: 50%;
    width: 22px;
    height: 22px;

    &.checked {
      border-color: #ac1140;

      .checked-round {
        background: #ac1140;
      }
    }

    .checked-round {
      display: inline-block;
      border-radius: 50%;
      width: 13px;
      height: 13px;
    }
  }
}
</style>
