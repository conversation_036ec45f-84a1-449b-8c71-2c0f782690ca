import { defineStore } from "pinia";
import { ref, reactive, nextTick, type ComponentPublicInstance } from "vue";
import { storeToRefs } from "pinia";
import { useGameStore } from "@/stores/game";
import { useGameFiltersStore } from "@/stores/gameFilters";
import { getGames } from "@/api/games";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";
import type { Game, PaginationState, FilterState } from "../views/game-categories/types";

const sortFunc = (a, b) => {
  if (a.big_images_set !== b.big_images_set) return b.big_images_set - a.big_images_set;
  if (a.tags === 0 && b.tags !== 0) return 1;
  if (a.tags !== 0 && b.tags === 0) return -1;
  if (a.tags !== b.tags) return a.tags - b.tags;
  if (a.balance_record !== b.balance_record) return b.balance_record - a.balance_record;
  if (a.sort !== b.sort) return b.sort - a.sort;
  if (a.name && b.name) {
    return a.name.localeCompare(b.name);
  }
};

// 定义 Casino 相关状态与逻辑的 Pinia Store
export const useCasinoStore = defineStore("casino", {
  state: () => ({
    // 常量配置
    CASINO_TABS: [
      { id: 1, name: "Baccarat" },
      { id: 2, name: "Roulette" },
      { id: 3, name: "Blackjack" },
    ] as Array<{ id: number; name: string }>,

    // 响应式状态
    isDataLoading: false,
    isLoadingMore: false,
    currentIndex: 0,
    dialogVisible: false,
    casinoGameList: [] as Game[],

    // 分页状态
    paginationState: {
      pageSize: 200,
      pageMap: { 1: 1, 2: 1, 3: 1 },
    } as PaginationState,

    // 滚动位置与DOM缓存
    tabScrollPositions: {} as Record<number, number>,
    tabRefs: {} as Record<number, HTMLElement>,
  }),

  getters: {
    // 筛选状态 - 使用全局筛选状态
    filterState(): FilterState {
      return useGameFiltersStore().filterState;
    },
    // 计算赌场游戏类型ID
    casinoGameId(): string {
      const gameStore = useGameStore();
      const { gameTypes } = storeToRefs(gameStore);
      const casinoType = gameTypes.value.find((g: any) => g.name?.toLowerCase() === "casino");
      return (casinoType?.id && String(casinoType?.id)) || "";
    },

    // 计算每个Tab的过滤后游戏列表
    filteredTabs(): Array<{
      id: number;
      name: string;
      allGames: Game[];
      pagedGames: Game[];
      hasMore: boolean;
    }> {
      return this.CASINO_TABS.map((tab) => {
        // 1. 按table_group筛选
        let allGames = this.casinoGameList.filter((g: Game) => g.table_group === tab.id);

        // 2. 厂商筛选
        if (
          this.filterState.selectedCategories.length &&
          !this.filterState.selectedCategories.includes("all")
        ) {
          allGames = allGames.filter((g: Game) =>
            this.filterState.selectedCategories.includes(g.company_id)
          );
        }

        // 3. 搜索过滤
        if (this.filterState.searchValue) {
          const keyword = this.filterState.searchValue.toLowerCase();
          allGames = allGames.filter((g: Game) => (g.name || "").toLowerCase().includes(keyword));
        }
        // / 过滤掉隐藏的游戏ID
        allGames = allGames.filter((game: Game) => !useGameStore().hideList.includes(game.id));

        // 4. 分页处理
        const page = this.paginationState.pageMap[tab.id] ?? 1;
        const pagedGames = allGames.slice(0, page * this.paginationState.pageSize);

        return {
          ...tab,
          allGames,
          pagedGames,
          hasMore: allGames.length > pagedGames.length,
        };
      });
    },

    // 判断是否有筛选条件
    hasFilters(): boolean {
      return useGameFiltersStore().hasFilters;
    },
  },

  actions: {
    // 获取赌场游戏数据
    async fetchCasinoGames() {
      const casinoId = this.casinoGameId;
      if (!casinoId) return;

      this.isDataLoading = true;
      showZLoading();

      try {
        const res = await getGames({ type: casinoId });
        const gameData = res?.data || res || [];
        gameData.sort(sortFunc);
        this.casinoGameList = gameData;

        await nextTick();
      } catch (error) {
        this.casinoGameList = [];
      } finally {
        this.isDataLoading = false;
        closeZLoading();
      }
    },

    // 加载更多游戏
    async loadMoreGames(tabId: number) {
      this.isLoadingMore = true;
      showZLoading();

      try {
        // await new Promise((resolve) => setTimeout(resolve, 300)); // 模拟加载延迟
        const currentPage = this.paginationState.pageMap[tabId] ?? 1;
        this.paginationState.pageMap[tabId] = currentPage + 1;
        console.log(`Casino 加载更多游戏 - Tab: ${tabId}, 页码: ${currentPage + 1}`);
      } finally {
        closeZLoading();
        this.isLoadingMore = false;
      }
    },

    // 处理Tab切换
    handleTabChange(index: number, tabRefs: Record<number, HTMLElement>) {
      // 保存当前Tab的滚动位置
      const currentTab = this.filteredTabs[this.currentIndex];
      // if (currentTab && tabRefs[currentTab.id]) {
      //   this.tabScrollPositions[currentTab.id] = tabRefs[currentTab.id].scrollTop;
      // }
      this.currentIndex = index;
    },

    // 设置Tab的DOM引用并恢复滚动位置
    setTabRef(el: Element | ComponentPublicInstance | null, tabId: number) {
      if (el && el instanceof HTMLElement) {
        this.tabRefs[tabId] = el;
        if (this.tabScrollPositions[tabId]) {
          nextTick(() => {
            el.scrollTop = this.tabScrollPositions[tabId];
          });
        }
      }
    },

    // 处理滚动加载
    async handleScroll(e: Event, tabId: number) {
      const el = e.target as HTMLElement;
      this.tabScrollPositions[tabId] = el.scrollTop;
      // 距离底部小于50px时加载更多
      if (el.scrollHeight - el.scrollTop - el.clientHeight < 50) {
        const tab = this.filteredTabs.find((t) => t.id === tabId);
        if (tab?.hasMore && !this.isLoadingMore) {
          await this.loadMoreGames(tabId);
        }
      }
    },

    // 更新游戏收藏状态
    async updateGameLike(updatedGame: any) {
      try {
        const index = this.casinoGameList.findIndex(
          (game) => game.game_id === updatedGame.game_id || game.id === updatedGame.id
        );
        if (index !== -1) {
          const newGame = { ...this.casinoGameList[index], is_like: updatedGame.is_like };
          this.casinoGameList.splice(index, 1, newGame); // 触发响应式更新
        } else {
          console.warn("Game not found in casinoGameList:", updatedGame);
        }
      } catch (error) {
        console.error("Failed to update like status:", error);
      }
    },

    // 根据路由参数初始化Tab位置
    initializeTabFromRoute(routeQueryId: any) {
      const routeId = routeQueryId;
      if (!routeId) return;

      const tabId = Number(routeId);
      if (isNaN(tabId) || tabId <= 0) {
        console.warn(`Invalid tab id: ${routeId}, using default tab`);
        return;
      }

      const tabIndex = this.CASINO_TABS.findIndex((tab) => tab.id === tabId);
      if (tabIndex !== -1) {
        console.log(`Setting initial tab to index ${tabIndex} for id ${tabId}`);
        this.currentIndex = tabIndex;
      } else {
        console.warn(`Tab with id ${tabId} not found, using default tab`);
      }
    },
  },
});
