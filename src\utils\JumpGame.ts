import { showToast } from "vant";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";
import { getOS } from "@/utils/core/tools";
import { getBitValue } from "@/utils/core/tools";
import { useGlobalStore } from "@/stores/global";
import { useGameStore } from "@/stores/game";
import { CHANEL_TYPE, MAINTENANCETIPCODE } from "@/utils/config/GlobalConstant";
import { gameItemLogin, getGameInfo } from "@/api/games";
import { getGlobalDialog } from "@/enter/vant";
import { KycMgr, InGameType } from "@/utils/KycMgr";
import { getToken } from "@/utils/auth";
import router from "@/router";
import { MobileWindowManager } from "@/utils/managers/MobileWindowManager";
import { h, render } from "vue";
import SafariTipsDialog from "@/components/SafariTipsDialog/index.vue";

const $dialog = getGlobalDialog();
const globalStore = useGlobalStore();

/**
 * 检测是否为 iOS Safari 浏览器
 */
const isIOSSafari = (): boolean => {
  const userAgent = navigator.userAgent;
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);
  const isSafari = /Safari/.test(userAgent) && !/Chrome|CriOS|FxiOS/.test(userAgent);
  return isIOS && isSafari;
};

/**
 * 检查今天是否已经显示过 Safari 提示
 */
const hasShownSafariTipsToday = (): boolean => {
  const today = new Date().toDateString();
  const lastShownDate = localStorage.getItem("safari_tips_shown_date");
  return lastShownDate === today;
};

/**
 * 标记今天已经显示过 Safari 提示
 */
const markSafariTipsShownToday = (): void => {
  const today = new Date().toDateString();
  localStorage.setItem("safari_tips_shown_date", today);
};

/**
 * 显示 Safari 弹窗提示
 */
const showSafariTipsDialog = (onGotIt: () => void): Promise<void> => {
  return new Promise((resolve) => {
    const container = document.createElement("div");
    document.body.appendChild(container);

    const vnode = h(SafariTipsDialog, {
      modelValue: true,
      onGotIt: (dontShowToday: boolean) => {
        if (dontShowToday) {
          markSafariTipsShownToday();
        }
        cleanup();
        onGotIt();
        resolve();
      },
      onViewInstructions: () => {
        cleanup();
        // 将回调函数存储到 window 对象，以便在指导页面中使用
        (window as any).__safariTipsCallback = onGotIt;
        router.push("/system/safari-instructions");
        resolve();
      },
      onClose: () => {
        cleanup();
        resolve();
      },
    });

    const cleanup = () => {
      render(null, container);
      document.body.removeChild(container);
    };

    render(vnode, container);
  });
};

const showMaintenancetip = (response: any) => {
  $dialog({
    title: "Maintenance",
    message: response.msg,
    confirmText: "Done",
    showCancelButton: false,
    onConfirm() {
      const currentRoute = router.currentRoute.value;
      if (currentRoute.path === "/game-webview") {
        router.back();
      }
    },
  });
};

//用二进制的值做的对应
export enum OS_JumpOpen {
  android_h5 = 1,
  android_app,
  android_maya,
  ios_h5,
  ios_app,
  ios_maya,
}

// 异常处理
export const handleException = (response: any) => {
  if (!response) return false;
  if (response.code == MAINTENANCETIPCODE || response.code == 102041) {
    showMaintenancetip(response);
    return false;
  } else if (response.code == 102046) {
    showToast("Sorry, the current game does not exist");
    return false;
  } else if (response.code == 102010) {
    $dialog({
      message: "Invalid access token. Please login again.",
      confirmText: "Done",
      showCancelButton: false,
      onConfirm() {
        // 退出登陆
        globalStore.loginOut();
      },
    });
    return false;
  } else if ([1200, 101013, 9].includes(response.code)) {
    //kyc没有审核通过 提示Please perform KYC verification first
    showToast(response.msg);
    return false;
  } else if (response.code == 500 || response.code == 401) {
    showToast("The game cannot be accessed, please contact customer service.");
    return false;
  }
  return true;
};

const goNext = async (data: any) => {
  // 先关闭可能残留的 loading
  closeZLoading();
  // 确保 showZLoading 能正常挂载
  showZLoading({
    duration: 0,
  });

  let gameInfo = data;
  // 其它入口
  if (!data.company_id || !data.third_party_game_id) {
    const gameInfoResponse = await getGameInfo({ id: data.id });
    const gameInfoList = gameInfoResponse.data || gameInfoResponse;
    if (gameInfoList && gameInfoList?.length > 0) {
      gameInfo = gameInfoList[0];
    } else {
      closeZLoading();
      return;
    }
  }

  let { is_jump, id, third_party_game_id, company_id } = gameInfo;
  // 这块取数有疑问
  let isJump = is_jump ? getGameJumpType(is_jump) : getGameJumpType(is_jump);

  // 初始化预打开窗口变量，但暂时不打开窗口
  let preOpenedWindow: Window | null = null;

  let params: any;
  if (id) {
    params = {
      game_id: id,
      company_id: company_id,
    };
  } else {
    params = {
      third_game_id: third_party_game_id,
      company_id: company_id,
    };
  }

  try {
    const apiResponse = await gameItemLogin(params);
    // 提取实际的响应数据
    const response = apiResponse.data || apiResponse;
    closeZLoading();
    const isPass = handleException(response);
    if (!isPass) {
      return;
    }

    let go_handle = false;
    if (isJump) {
      go_handle = true;
    }
    if (response.game_html) {
      go_handle = true; //类似pg游戏 直接走外跳逻辑
    }

    // 如果需要外跳但没有有效的游戏 URL 或 HTML，直接返回
    if (go_handle && !response.game_url && !response.game_html) {
      showToast(response.msg || "The game cannot be accessed, please contact customer service.");
      return;
    }

    // 接口请求成功且验证通过后，如果需要外跳且是移动端，才预打开窗口
    // 但如果是 iOS Safari，需要等用户点击 Got it 后再预打开窗口
    if (go_handle && MobileWindowManager.isMobile() && !isIOSSafari()) {
      preOpenedWindow = MobileWindowManager.preOpenWindow();
      if (!preOpenedWindow) {
        console.warn("Popup blocked, will fallback to current window navigation");
      }
    }
    if (go_handle) {
      // 外跳 - 需要打开新窗口
      await handleJumpOpen(response, preOpenedWindow);
    } else {
      // 内跳 - 只是路由跳转，关闭预打开的窗口（如果有）
      MobileWindowManager.closeWindow(preOpenedWindow);
      const { third_game_id, company_id, game_id } = params;
      router.push({
        path: "/game-webview",
        query: {
          third_game_id,
          company_id,
          id: game_id,
        },
      });
    }
  } catch (err) {
    closeZLoading();
    showToast("The game cannot be accessed, please contact customer service.");
    // 如果有预打开的窗口，关闭它
    MobileWindowManager.closeWindow(preOpenedWindow);
  }
};

/**
 * 游戏跳转方法
 * 自动处理移动端兼容性，无需传递额外参数
 * @param data 游戏数据
 */
export const jumpGame = async (data: { id: number; [key: string]: any }) => {
  if (!getToken()) {
    router.push("/login");
    return;
  }

  //这里拦截一下 验证是否完整版 kyc 验证
  KycMgr.instance.verifyKyc(InGameType.GoThirdGame, (isVerity) => {
    if (isVerity) {
      goNext(data);
    }
  });
};

const handleJumpOpen = async (response: any, preOpenedWindow: Window | null = null) => {
  if (!response) return;

  // 定义实际的外跳逻辑
  const executeJump = () => {
    // 如果是 iOS Safari 且没有预打开窗口，现在创建一个
    let windowToUse = preOpenedWindow;
    if (isIOSSafari() && !windowToUse && response.game_url) {
      windowToUse = MobileWindowManager.preOpenWindow();
    }

    if (response.game_url) {
      // 使用 MobileWindowManager 进行安全的 URL 导航
      // 传递预打开的窗口以获得更好的移动端体验
      const success = MobileWindowManager.navigateToUrl(response.game_url, windowToUse);

      if (!success) {
        console.error("Failed to navigate to game URL:", response.game_url);
        showToast("Failed to open game, please try again");
      }
    } else if (response.game_html) {
      // 对于 HTML 内容，关闭预打开的窗口（如果有）并在当前窗口显示
      MobileWindowManager.closeWindow(windowToUse);

      window.history.pushState({ page: window.location.href }, "Nustar", window.location.href);
      window.document.open();
      window.document.write(response.game_html);
      window.document.close();
    }
  };

  // 检测是否为 iOS Safari，且配置允许显示弹窗，且今天还没显示过提示，则显示弹窗
  const gameStore = useGameStore();
  const shouldShowTips =
    isIOSSafari() && gameStore.iosPopUpWindow === 1 && !hasShownSafariTipsToday();

  if (shouldShowTips) {
    await showSafariTipsDialog(executeJump);
  } else {
    // 不满足条件时，直接执行外跳
    executeJump();
  }
};

const getGameJumpType = (isJump: number) => {
  if (!isJump) return false;
  const platom = getOS();
  const globalStore = useGlobalStore();
  if (platom === "android") {
    if (globalStore.channel === CHANEL_TYPE.MAYA) {
      return getBitValue(isJump, OS_JumpOpen.android_maya) == 1;
    } else {
      return getBitValue(isJump, OS_JumpOpen.android_h5) == 1;
    }
  } else if (platom === "ios") {
    if (globalStore.channel === CHANEL_TYPE.MAYA) {
      return getBitValue(isJump, OS_JumpOpen.ios_maya) == 1;
    } else {
      return getBitValue(isJump, OS_JumpOpen.ios_h5) == 1;
    }
  } else {
    return getBitValue(isJump, OS_JumpOpen.android_h5) == 1;
  }
};
