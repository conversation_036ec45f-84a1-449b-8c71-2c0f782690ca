<!--
  第三方登录组件
  支持 Google 和 Facebook 登录
-->
<template>
  <div class="third-party-login">
    <!-- Google 登录 -->
    <button v-if="loginConfig.login_google === 0" class="login-provider-button" @click="handleGoogleLogin"
      aria-label="Login with Google">
      <GoogleIcon class="provider-icon" />
    </button>

    <!-- Facebook 登录 -->
    <button v-if="loginConfig.login_facebook === 0" class="login-provider-button" @click="handleFacebookLogin"
      aria-label="Login with Facebook">
      <FacebookIcon class="provider-icon" />
    </button>
  </div>
</template>

<script setup lang="ts">
/**
 * 第三方登录组件
 */
import { storeToRefs } from "pinia";
import { useGlobalStore } from "@/stores/global";
import GoogleIcon from "@/assets/icons/login/google-logo.svg";
import FacebookIcon from "@/assets/icons/login/facebook-logo.svg";


const globalStore = useGlobalStore();
const { loginConfig } = storeToRefs(globalStore);

const emit = defineEmits<{
  /** Google 登录 */
  'google-login': [];
  /** Facebook 登录 */
  'facebook-login': [];
}>();

/**
 * 处理 Google 登录
 */
const handleGoogleLogin = () => {
  emit('google-login');
};

/**
 * 处理 Facebook 登录
 */
const handleFacebookLogin = () => {
  emit('facebook-login');
};
</script>

<style scoped lang="scss">
.third-party-login {
  display: flex;
  justify-content: center;
  gap: 40px;

  .login-provider-button {
    background: none;
    border: none;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }

    .provider-icon {
      width: 40px;
      height: 40px;
      display: block;
    }
  }
}
</style>
