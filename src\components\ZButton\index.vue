<template>
  <van-button class="z-button" :type="type" :loading="loading" :disabled="disabled || loading" :block="block"
    :round="true" @click="handleClick">
    <slot />
  </van-button>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { throttle as lodashThrottle } from "lodash-es";
import type { ButtonType } from "vant";

const props = defineProps({
  // 类型
  type: {
    type: String as PropType<ButtonType>,
    default: "primary",
    validator: (value: string) => ["default", "primary"].includes(value),
  },
  // 是否禁用按钮
  disabled: {
    type: Boolean,
    default: false,
  },
  // 是否为块级元素
  block: {
    type: Boolean,
    default: false,
  },
  // 按钮点击事件回调，支持同步或异步操作
  click: {
    type: Function as PropType<(event: Event) => void | Promise<void>>,
    default: null,
  },
  // 节流等待时长，单位毫秒（默认 200ms）
  throttleTime: {
    type: Number,
    default: 200,
  },
  // 是否启用节流处理，防止短时间内重复点击
  useThrottle: {
    type: Boolean,
    default: true,
  },
});

const loading = ref(false);

// 计算属性，决定是否使用节流
const shouldUseThrottle = computed(() => props.useThrottle !== false && props.throttleTime > 0);

// 节流处理函数
const throttledClickHandler = computed(() => {
  const handler = async (event: Event) => {
    if (props.click) {
      return props.click(event);
    }
  };
  if (shouldUseThrottle.value) {
    return lodashThrottle(handler, props.throttleTime, { leading: true, trailing: false });
  } else {
    return handler;
  }
});

// 统一的点击处理逻辑
const handleClick = (event: Event) => {
  // 如果按钮禁用或者处于加载状态则不处理点击
  if (props.disabled || loading.value) return;
  const back = throttledClickHandler.value(event);
  if (back && back instanceof Promise) {
    loading.value = true;
    back.finally(() => {
      loading.value = false;
    });
  }
};
</script>

<style scoped lang="scss">
.z-button {
  height: 48px;
  width: 100%;
  border-radius: 28px;
  font-weight: 600;
  box-shadow: none;
  border: none;
  color: #FFF;

  /* 按钮内文字 */
  font-family: Inter;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;

  &.van-button--default {
    background: #f5f7fd;
    color: #222;
  }

  &.van-button--primary {
    background: var(--red-color);
    color: #fff;
  }

  &:disabled {
    background: #E8B4CB !important;
    color: #FFFFFF !important;
    cursor: not-allowed;
    opacity: 1;
  }

  &:active:not(:disabled) {
    opacity: 0.8;
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}
</style>
