<template>
  <ZPopOverlay :show="showActivityBonusTip">
    <div class="content" :style="{ backgroundImage: `url(${activityBonus4Image})` }">
      <div class="tips">{{ promotionText }}</div>
      <div class="bonus-wrap">
        <IconCoin class="icon" />
        <span class="bonus">{{ formattedBonusAmount }}</span>
      </div>
      <div class="date">{{ rewardDateText }}</div>
      <div class="btn" :style="{ backgroundImage: `url(${activityBonus2Image})` }" @click="confirmClick"
        ref="confirmBtnRef"></div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import { init_enum_typestr } from '@/utils/GlobalScript';
import { AWARD_NAME } from '@/utils/config/GlobalConstant';
import { getAdjustmentList, postActivityBonusReceive, postAdjustmentBonusRecordReceive } from "@/api/activity";
import { useGlobalStore } from "@/stores/global";
import { getToken } from '@/utils/auth';
// 导入图片资源，确保与预加载使用相同的路径
import activityBonus2Image from '@/assets/images/popDialog/activityBonus2.png';
import activityBonus4Image from '@/assets/images/popDialog/activityBonus4.png';

const globalStore = useGlobalStore();
const autoPopMgrStore = useAutoPopMgrStore();
const { showActivityBonusTip, popActivityBonus, } = storeToRefs(autoPopMgrStore);

// 使用更具语义的变量名
const promotionText = ref('');      // 奖励描述文字
const bonusAmountText = ref('');      // 奖励金额文本
const rewardDateText = ref('');       // 奖励日期文本

const emit = defineEmits(['start-coin-animation']);
const confirmBtnRef = ref<HTMLElement | null>(null);

// 标记是否正在发送领奖请求
const isSending = ref(false);

// 格式化金额，如果 bonusAmountText 为数字则以本地格式显示，否则显示 "--"
const formattedBonusAmount = computed(() => {
  const num = Number(bonusAmountText.value?.replace("-", ""));
  return num >= 0 ? num.toLocaleString() : "--";
});

// 关闭弹窗，清理后调用余额刷新和销毁当前弹窗
const closeCommonTips = (bGoldAni?: boolean) => {
  // 移除本次奖励，可用 shift 删除首个数据
  autoPopMgrStore.popActivityBonus?.shift();
  showActivityBonusTip.value = false;
  if (bGoldAni) {
    emit('start-coin-animation', confirmBtnRef.value);
  }
  setTimeout(() => {
    if (autoPopMgrStore.popActivityBonus.length > 0) {
      showActivityBonusTip.value = true;
    } else {
      AutoPopMgr.destroyCurrentPopup();
    }
  }, 2000)
}

// 确认领奖处理
const confirmClick = async () => {
  if (!getToken()) return;
  if (isSending.value) return
  isSending.value = true;
  const firstBonus = popActivityBonus.value[0];
  const params = {
    type: firstBonus?.type || '',
    id: firstBonus.id,
  };
  // 根据 activity_name 判断使用哪一个 API
  const apiFn = firstBonus?.activity_name
    ? postAdjustmentBonusRecordReceive
    : postActivityBonusReceive;
  try {
    await apiFn(params);
    closeCommonTips(true)
  } catch (error) {
    closeCommonTips()
  } finally {
    isSending.value = false
  }
};

// 初始化调账领奖数据
const initBonusRecordData = () => {
  const firstBonus = popActivityBonus.value[0];
  const bonusTypeStr = firstBonus?.activity_name;
  promotionText.value = `You received a bonus in ${bonusTypeStr} promotion: `;
  bonusAmountText.value = firstBonus?.bonus ? `${firstBonus.bonus}` : '0';
  rewardDateText.value = `Reward date: ${firstBonus?.bonus_date || '--'}`;
};

// 获取并返回调账列表数据
const fetchAdjustmentList = async () => {
  try {
    return await getAdjustmentList({});
  } catch (error) {
    return [];
  }
};

// 初始化普通奖励数据（非调账奖励）
const initNormalBonusData = async () => {
  const firstBonus = popActivityBonus.value[0];
  let updateType = firstBonus?.type;
  // 优先获取调账相关枚举数据
  let adjustmentEnum = await fetchAdjustmentList();
  let typeStr = '';
  if (adjustmentEnum && adjustmentEnum.length > 0) {
    const targetType = adjustmentEnum.filter((ele) => ele.change_type == updateType);
    if (targetType.length > 0) {
      typeStr = targetType[0].title;
      if (!typeStr) {
        init_enum_typestr(updateType, typeStr, AWARD_NAME.FREE_REGISTRATION);
      }
      promotionText.value = `You received a bonus in ${typeStr} promotion: `;
    }
  } else {
    init_enum_typestr(updateType, typeStr, AWARD_NAME.FREE_REGISTRATION);
    promotionText.value = `You received a bonus in ${typeStr} promotion: `;
  }
  bonusAmountText.value = firstBonus?.bonus ? `${firstBonus.bonus}` : `0`;
  rewardDateText.value = `Reward date: ${firstBonus?.bonus_date || '--'}`;
};

// 总初始化函数，根据奖励数据类型进行区分处理
const initBonusData = async () => {
  const firstBonus = popActivityBonus.value[0];
  if (firstBonus?.activity_name && firstBonus?.id) {
    // 如果是调账奖励，则初始化调账数据
    initBonusRecordData();
  } else {
    await initNormalBonusData();
  }
};

// 监听弹窗显示和奖励数据的变化，初始化数据
watch(
  [() => autoPopMgrStore.showActivityBonusTip, () => autoPopMgrStore.popActivityBonus],
  ([show, bonusList]) => {
    if (show && bonusList?.length > 0) {
      initBonusData();
    }
  }
);

defineExpose({
  confirmBtnRef
});
</script>

<style lang="scss" scoped>
.content {
  width: 375px;
  height: 667px;
  padding: 20px;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  position: relative;

  .tips {
    width: 280px;
    position: absolute;
    top: 280px;
    left: 50%;
    transform: translateX(-50%);
    color: #222;
    font-family: Inter;
    font-size: 16px;
    font-weight: 500;
    line-height: 28px;
  }

  .date {
    position: absolute;
    top: 495px;
    left: 50%;
    transform: translateX(-50%);
  }

  .bonus-wrap {
    position: absolute;
    top: 350px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;

    .icon {
      margin-right: 10px;
    }

    .bonus {
      color: #222;
      font-family: D-DIN;
      font-size: 30px;
      font-weight: 700;
      line-height: 28px;
    }
  }

  .btn {
    width: 220px;
    height: 56px;
    overflow: hidden;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 100% 100%;
    position: absolute;
    top: 420px;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
  }

  .close {
    position: absolute;
    top: 550px;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
  }
}
</style>
