/**
 * 弹窗图片预加载器 - 解决弹窗图片加载慢的问题
 * 基于通用图片预加载工具构建
 */

import { imagePreloader, type PreloadOptions } from "@/utils/core/imagePreloader";

// 导入所有弹窗相关的静态图片
import turntableHead from "@/assets/images/turnTable/turntable-head.png";
import turntableBg from "@/assets/images/turnTable/turntable-bg.png";
import turntableChecked from "@/assets/images/turnTable/checked.png";
import turntableCenterBtn from "@/assets/images/turnTable/center-btn.png";
import turntableCenterBtnDisable from "@/assets/images/turnTable/center-btn-disable.png";
import turntableArrow from "@/assets/images/turnTable/arrow.png";
import turntableContent from "@/assets/images/turnTable/turntable-content.png";
// VIP 弹窗图片
import vip1 from "@/assets/images/popDialog/vip1.png";
import vip2 from "@/assets/images/popDialog/vip2.png";
// 红包弹窗图片
import envelope1 from "@/assets/images/popDialog/envelope1.png";
import envelope2 from "@/assets/images/popDialog/envelope2.png";
// 排行榜弹窗图片
import rankCasino from "@/assets/images/popDialog/rank_casino.png";
import rankCasino2 from "@/assets/images/popDialog/rank_casino2.png";
import rankJili from "@/assets/images/popDialog/rank_jili.png";
import rankJili2 from "@/assets/images/popDialog/rank_jili2.png";
// 活动奖励弹窗图片
import activityBonus2 from "@/assets/images/popDialog/activityBonus2.png";
import activityBonus3 from "@/assets/images/popDialog/activityBonus3.png";
import activityBonus4 from "@/assets/images/popDialog/activityBonus4.png";
import activityBonus5 from "@/assets/images/popDialog/activityBonus5.png";

export class PopupImagePreloader {
  private readonly staticImages: string[] = [
    // VIP 弹窗图片
    vip1,
    vip2,
    // 红包弹窗图片
    envelope1,
    envelope2,
    // 排行榜弹窗图片
    rankCasino,
    rankCasino2,
    rankJili,
    rankJili2,
    // 活动奖励弹窗图片
    activityBonus2,
    activityBonus3,
    activityBonus4,
    activityBonus5,

    // 转盘弹窗图片
    turntableHead,
    turntableBg,
    turntableChecked,
    turntableCenterBtn,
    turntableCenterBtnDisable,
    turntableArrow,
    turntableContent,
  ];

  private readonly cssBackgroundImages: string[] = [
    envelope1,
    envelope2,
    rankCasino,
    rankCasino2,
    rankJili,
    rankJili2,
    vip1,
    vip2,
    activityBonus2,
    activityBonus3,
    activityBonus4,
    activityBonus5,
    turntableContent,
  ];

  /**
   * 预加载所有弹窗图片
   */
  async preloadAll(onProgress?: (loaded: number, total: number) => void): Promise<void> {
    const options: PreloadOptions = {
      timeout: 8000,
      onProgress,
      concurrent: true,
      concurrency: 5,
      onError: (error, src) => {
        console.warn(`弹窗图片预加载失败: ${src}`, error);
      },
    };

    // 1. 先预加载 CSS 背景图片（最重要）
    await imagePreloader.preloadCSSBackgrounds(this.cssBackgroundImages, options);

    // 2. 预加载静态图片
    await imagePreloader.preloadList(this.staticImages, options);

    // 3. 预加载动态图片
    await this.preloadDynamicImages(options);

    // console.log("=== 弹窗图片预加载完成 ===");
  }

  /**
   * 预加载动态图片（转盘类型等）
   */
  private async preloadDynamicImages(options: PreloadOptions): Promise<void> {
    try {
      // console.log("开始预加载动态图片...");

      // 预加载转盘类型图片
      const turntableTypes = await this.getTurntableTypeImages();
      if (turntableTypes.length > 0) {
        await imagePreloader.preloadList(turntableTypes, options);
        // console.log(`转盘类型图片预加载完成: ${turntableTypes.length} 张`);
      }
    } catch (error) {
      console.warn("动态图片预加载失败:", error);
    }
  }

  /**
   * 获取转盘类型图片
   */
  private async getTurntableTypeImages(): Promise<string[]> {
    try {
      const imageModules = import.meta.glob("@/assets/images/turnTable/types.{png,jpg,svg}", {
        eager: true,
      });

      return Object.entries(imageModules).map(([, module]) => {
        return (module as { default: string }).default;
      });
    } catch (error) {
      console.warn("获取转盘类型图片失败:", error);
      return [];
    }
  }

  /**
   * 获取图片文件名
   */
  private getImageName(src: string): string {
    return src.split("/").pop() || src;
  }

  /**
   * 检查图片是否已预加载
   */
  isImageCached(src: string): boolean {
    return imagePreloader.isImageCached(src);
  }

  /**
   * 强制预加载指定图片
   */
  async forcePreloadImage(src: string): Promise<boolean> {
    try {
      const result = await imagePreloader.preloadSingle(src);
      // console.log(`强制预加载成功: ${this.getImageName(src)}`);
      return result;
    } catch (error) {
      console.warn(`强制预加载失败: ${this.getImageName(src)}`, error);
      return false;
    }
  }
}

// 导出单例实例
export const popupImagePreloader = new PopupImagePreloader();
