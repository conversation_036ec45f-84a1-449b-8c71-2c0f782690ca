<template>
  <div class="wallet-card-container">
    <!-- VIP header -->
    <div class="vip-header" :class="{ noVip: !isVip }">
      <div class="vip-tag">
        <span class="vip-logo"></span>
        <span class="divide-line"></span>
        <span class="vip-date" v-if="isVip">{{ vipStart }}-{{ vipEnd }}</span>
        <span v-else class="vip-style"> How To Become VIP? </span>
      </div>
      <span class="go-btn" @click="toVip">Go<span class="iconfont icon-qianjin"></span></span>
    </div>

    <!-- Balance section -->
    <div class="balance-section">
      <div class="balance-label">Total Balance (₱)</div>
      <div class="balance-amount">
        <Balance :coinSize="30" :balanceSize="28"></Balance>
        <ZIcon
          type="icon-shuaxin"
          :class="{ 'rotate-once': isRotating }"
          @click="handleRefresh"
          color=""
          :size="16"
        >
        </ZIcon>
      </div>

      <!-- Action buttons -->
      <div class="action-buttons">
        <ZButton class="btn-withdrawal" @click="showWithdrawDialog">
          <ZIcon type="icon-withdrawal" color="#ac1140"></ZIcon>
          Withdrawal
        </ZButton>
        <ZButton class="btn-deposit" @click="() => depositStore.openDialog()">
          <ZIcon type="icon-deposit"></ZIcon>
          Deposit
        </ZButton>
      </div>
    </div>
    <!-- 绑定手机号、支付密码 -->
    <VerifyDialogPreconditions
      v-model:showDialog="showVerifyDialogPreconditions"
      :succCallBack="handlePreconditionsConfirm"
    >
    </VerifyDialogPreconditions>
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";
import { onBeforeMount, ref } from "vue";
import { useGlobalStore } from "@/stores/global";
import { useDepositStore } from "@/stores/deposit";
import { useWithdrawStore } from "@/stores/withdraw";
import { throttleFirst } from "@/utils/core/tools";
import { useVerifyPreconditions } from "@/composables/useVerifyPreconditions";
import VerifyDialogPreconditions from "@/components/ZVerifyDialog/VerifyDialogPreconditions.vue";
import { KycMgr, InGameType, KycState } from "@/utils/KycMgr";

const depositStore = useDepositStore();
const withdrawStore = useWithdrawStore();
const globalStore = useGlobalStore();

const router = useRouter();
const isRotating = ref(false);

// 使用验证前置条件的 composable
const { showVerifyDialogPreconditions, verifyPreconditions, handlePreconditionsConfirm } =
  useVerifyPreconditions();

const isVip = ref(globalStore.userInfo.is_vip == 1);
const vipStart = ref(globalStore.userInfo.vip_start);
const vipEnd = ref(globalStore.userInfo.vip_end);

const getBackhall = async () => {
  isRotating.value = true;
  try {
    await globalStore.getBalance();
  } finally {
    isRotating.value = false;
  }
};

const handleRefresh = throttleFirst(getBackhall, 2000);

const toVip = () => {
  const vipReaded = localStorage.getItem("vipReaded");
  if (vipReaded) {
    router.push("/account/vip");
  } else {
    router.push("/account/vip-introduction");
  }
};

const showWithdrawDialog = () => {
  verifyPreconditions(async () => {
    // kyc 未验证
    // if (KycMgr.instance.kycState === KycState.NO_VERIFY) {
    //   // 这里非强制认证kyc
    //   if (KycMgr.instance.kycSimple === 0) {
    //     router.push(`/kyc/simple-form`);
    //   } else if (KycMgr.instance.kycSimple === 1) {
    //     router.push(`/kyc/normal-form`);
    //   }
    //   return
    // }
    withdrawStore.openDialog();
  });
};
</script>

<style lang="scss" scoped>
.wallet-card-container {
  border-radius: 12px;
  color: #333 !important;

  > div {
    box-sizing: border-box;
  }
}

.vip-style {
  color: #fff;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.noVip {
  .vip-tag {
    font-weight: 700 !important;

    span.vip-logo {
      background-position: left -13vw top 1.2vw;
    }
  }

  .go-btn {
    color: #333;
    background: linear-gradient(to right, #fff, rgba(208, 208, 208, 1));
    // color: #5F4100;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    width: 55px;
    line-height: 26px;
  }
}

.vip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 12px;
  color: #fff;
  font-size: 14px;
  padding: 4px 12px;
  background: linear-gradient(to right, rgba(105, 60, 0, 1), rgba(26, 13, 0, 1));

  &.noVip {
    background: linear-gradient(to right, rgba(85, 85, 85, 1), rgba(0, 0, 0, 1));
  }

  .vip-tag {
    display: flex;
    align-items: center;
    font-size: 14px;

    .vip-logo {
      width: 16.667vw;
      height: 10.667vw;
      background-image: url("@/assets/images/account/coin_img.png");
      background-repeat: no-repeat;
      background-size: 39.467vw 21.467vw;
      background-position: left 0vw top -11.8vw;
    }

    span.vip-text {
      color: #face8e;
      font-size: 20px;
    }

    .divide-line {
      margin: 0 6px;
      width: 1px;
      height: 12px;
      background-color: rgba(255, 255, 255, 0.6);
    }
  }

  .vip-date {
    color: #fff;
    font-family: DIN;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }
}

.go-btn {
  display: inline-block;
  width: 55px;
  line-height: 26px;
  border-radius: 999px;
  text-align: center;
  background: linear-gradient(to right, rgba(255, 243, 215, 1), rgba(255, 228, 167, 1));
  color: #5f4100 !important;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;

  .icon-qianjin {
    margin-left: 2px;
  }
}

.balance-section {
  margin-top: 10px;
  background: #fff;
  border-radius: 12px;
  padding: 12px;

  .balance-label {
    color: #666666;
    margin-bottom: 8px;
    color: #666;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .balance-amount {
    display: flex;
    align-items: center;
    gap: 8px;

    .amount {
      font-weight: 700;
      color: #222;
      font-family: D-DIN;
      font-size: 28px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      margin-right: 4px;
    }

    .icon-shuaxin {
      color: rgba(153, 153, 153, 1);
      display: inline-block;
      transform: rotate(0deg);
    }

    /* 定义一次旋转的关键帧动画 */
    @keyframes rotateOnce {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }

    /* 触发动画的类名 */
    .rotate-once {
      animation: rotateOnce 0.5s linear;
      /* 0.5秒完成一次旋转 */
      /* 动画结束后保持最终状态 */
      animation-fill-mode: forwards;
    }
  }
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 16px;

  button {
    flex: 1;
    border-radius: 999px;
    border: none;
    font-size: 16px;
    font-weight: 700;

    &.btn-withdrawal {
      display: flex;
      background: #ac11400d;
      color: #ac1140;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: center;
    }

    &.btn-deposit {
      display: flex;
      // background: rgba(172, 17, 64, 1);
      // color: #fff;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
