<template>
  <div class="line-item" @click="$emit('click')">
    <div class="left">
      <div class="icon">
        <slot name="icon"></slot>
      </div>
      <div class="text-value">
        <span class="text">{{ text }}</span>
        <span v-if="valueStatus === 'normal'" :style="{ ...valueStyle }" class="value normal">{{
          value
        }}</span>
        <span v-else-if="valueStatus === 'set'" class="value set">{{ value }}</span>
        <span v-else-if="valueStatus === 'not-set'" class="value not-set">Not Set</span>
        <span v-else-if="valueStatus === 'not-verify'" class="value not-verify">Not Verify</span>
        <span v-else-if="valueStatus === 'not-show'" class="value not-show">Not Show</span>
      </div>
    </div>
    <div class="arrow" v-if="showArrow">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z" fill="currentColor" />
      </svg>
    </div>
    <div :style="rightStyle" v-if="rightText">{{ rightText }}</div>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from "vue";

// 临时变量: 已设置正常显示,已设置但不显示,未设置，未验证，不显示
type ValueStatus = "normal" | "set" | "not-set" | "not-verify" | "not-show";
defineProps({
  text: {
    type: String,
    required: true,
  },
  value: {
    type: String,
    required: false,
  },
  valueStatus: {
    type: String as PropType<ValueStatus>,
    required: true,
    // 值和类型匹配规则 --优化
    default: (value: string) => {
      if (value) {
        return "normal";
      } else {
        return "not-set";
      }
    },
  },
  showArrow: {
    type: Boolean,
    default: true,
  },
  rightText: {
    type: String,
    default: "",
  },
  rightStyle: {
    type: Object,
    default: { color: "#666" },
  },
  valueStyle: {
    type: Object,
    default: {},
  },
});

defineEmits(["click"]);
</script>

<style lang="scss" scoped>
.line-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  cursor: pointer;

  .left {
    display: flex;
    align-items: center;
    gap: 12px;

    .icon {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333;
      width: 44px;
      height: 44px;
      border-radius: 50%;
      // background-color: #f5f5f5;
    }

    .text-value {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .text {
        color: #222;

        /* 列表内主标题 */
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
      }

      .value {
        color: #999;
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }

      .not-set {
        color: rgba(255, 72, 72, 1);
      }
    }
  }

  .arrow {
    display: flex;
    align-items: center;
    color: #999;
  }

  &:active {
    background: rgba(0, 0, 0, 0.05);
  }
}
</style>
