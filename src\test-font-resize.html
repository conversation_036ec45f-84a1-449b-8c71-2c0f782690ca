<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Resize Test</title>
    <style>
        .test-btn {
            background: #fff;
            color: #ac1140;
            width: 92px;
            height: 36px;
            padding: 0 8px;
            border-radius: 100px;
            font-weight: 700;
            white-space: nowrap;
            transition: font-size 0.2s ease;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            border: 2px solid #ac1140;
            margin: 10px;
            cursor: pointer;
        }
        
        .controls {
            margin: 20px;
        }
        
        button {
            margin: 5px;
            padding: 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>Font Auto-Resize Test</h1>
    
    <div class="controls">
        <button onclick="changeText('Download')">Short Text</button>
        <button onclick="changeText('Download Now')">Medium Text</button>
        <button onclick="changeText('Download App')">Long Text</button>
        <button onclick="changeText('Download Application')">Very Long Text</button>
        <button onclick="changeText('Download Application Now')">Extra Long Text</button>
    </div>
    
    <div class="test-btn" id="testBtn">
        Download
    </div>
    
    <div id="info">
        <p>Current text: <span id="currentText">Download</span></p>
        <p>Current font size: <span id="currentSize">14px</span></p>
    </div>
    
    <script>
        function adjustFontSize(element, text) {
            // 设置初始字体大小
            let fontSize = 14; // 最大字体大小
            const minFontSize = 8; // 最小字体大小
            const maxWidth = 92 - 16; // 按钮宽度减去padding (92px - 16px)
            
            // 创建临时元素来测量文本宽度
            const tempElement = document.createElement('div');
            tempElement.style.position = 'absolute';
            tempElement.style.visibility = 'hidden';
            tempElement.style.whiteSpace = 'nowrap';
            tempElement.style.fontWeight = '700';
            tempElement.style.fontFamily = getComputedStyle(element).fontFamily;
            tempElement.textContent = text;
            document.body.appendChild(tempElement);
            
            // 逐步减小字体大小直到文本适合容器
            while (fontSize >= minFontSize) {
                tempElement.style.fontSize = fontSize + 'px';
                const textWidth = tempElement.offsetWidth;
                
                if (textWidth <= maxWidth) {
                    break;
                }
                fontSize -= 0.5;
            }
            
            // 清理临时元素
            document.body.removeChild(tempElement);
            
            // 应用计算出的字体大小
            const finalSize = Math.max(fontSize, minFontSize);
            element.style.fontSize = finalSize + 'px';
            
            console.log(`Text: "${text}", Font size: ${finalSize}px`);
            return finalSize;
        }
        
        function changeText(text) {
            const element = document.getElementById('testBtn');
            element.textContent = text;
            
            const newSize = adjustFontSize(element, text);
            
            // 更新信息显示
            document.getElementById('currentText').textContent = text;
            document.getElementById('currentSize').textContent = newSize + 'px';
        }
        
        // 初始化
        window.addEventListener('load', () => {
            const element = document.getElementById('testBtn');
            adjustFontSize(element, 'Download');
        });
    </script>
</body>
</html>
