<script setup lang="ts">
defineOptions({ name: "CasinoCate" });

import { ref, onMounted, onActivated } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useCasinoStore } from "@/stores/gameCasino";
import CustomNavbar from "./Components/CustomNavbar.vue";
import { useGameFiltersStore } from "@/stores/gameFilters";
import GameItem from "@/components/GameItem.vue";

const casinoStore = useCasinoStore();
const gameFiltersStore = useGameFiltersStore();

const route = useRoute();
const router = useRouter();
const navbarRef = ref();

// 搜索、筛选、清除直接操作全局 store
const handleConfirmFilters = (categories: string[] = [], providerDetails: any[] = []) => {
  gameFiltersStore.setSelectedCategories(categories, router, route);
};
const handleClearFilters = () => {
  gameFiltersStore.clearFilters(router, route);
};
const handleSearch = (value: string) => {
  gameFiltersStore.setSearchValue(value || "", router, route);
};

const shouldShowNoData = (tab: any) => {
  return !casinoStore.isDataLoading && (!tab.pagedGames || !tab.pagedGames.length);
};

onMounted(() => {
  casinoStore.fetchCasinoGames();
});

onActivated(() => {
  casinoStore.initializeTabFromRoute(route.query.id);
  navbarRef.value?.forceRefresh?.();
  gameFiltersStore.syncToQuery(router, route); // 激活时同步搜索参数到地址栏
});
</script>

<template>
  <ZPage>
    <div class="categories-container">
      <div class="nav-bar-effect">
        <CustomNavbar
          ref="navbarRef"
          class="nav-bar"
          :confirm="handleConfirmFilters"
          v-model:visible="casinoStore.dialogVisible"
          @search="handleSearch"
        />
      </div>
      <div class="categories">
        <div class="categories-tabs">
          <van-tabs
            v-model:active="casinoStore.currentIndex"
            @change="casinoStore.handleTabChange"
            swipeable
            shrink
            line-height="0"
            background="transparent"
          >
            <van-tab v-for="tab in casinoStore.filteredTabs" :key="tab.id" :title="tab.name">
              <div class="games-container" @scroll="casinoStore.handleScroll($event, tab.id)">
                <van-row
                  class="games-grid"
                  gutter="12"
                  v-if="tab.pagedGames && tab.pagedGames.length > 0"
                >
                  <van-col v-for="game in tab.pagedGames" :key="`${game.id}`" :span="8">
                    <GameItem :game="game as any" @updateLike="casinoStore.updateGameLike" />
                  </van-col>
                </van-row>
                <div v-if="shouldShowNoData(tab)" class="no-data">
                  <template v-if="casinoStore.hasFilters">
                    <div>
                      <ZNoData text="Your filters has returned no results"></ZNoData>
                      <div @click="handleClearFilters" class="clear-filters-btn">Clear Filters</div>
                    </div>
                  </template>
                  <template v-else>
                    <ZNoData text="No Record"></ZNoData>
                  </template>
                </div>
              </div>
            </van-tab>
          </van-tabs>
        </div>
      </div>
    </div>
  </ZPage>
</template>

<style lang="scss" scoped>
.categories-container {
  height: 100%;

  .nav-bar-effect {
    min-height: 60px;

    .nav-bar {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 100;
      backdrop-filter: blur(10px);
      background-color: rgba(255, 255, 255, 0.9);
    }
  }

  .categories {
    height: calc(100vh - 60px);
    overflow: hidden;
    background: linear-gradient(to bottom, #ffffff, #f4f8fb 20%);

    .categories-tabs {
      height: 100%;

      &:deep(.van-tabs) {
        height: 100%;

        .van-tabs__nav {
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(12px);
          position: sticky;
          top: 0;
          z-index: 10;
        }

        .van-tabs__content {
          height: calc(100% - 44px);
        }

        .van-tab__panel {
          height: 100%;
        }
      }

      .games-container {
        height: 100%;
        overflow-y: auto;
        padding: 12px;
        scroll-behavior: auto;
        -webkit-overflow-scrolling: touch;

        .games-grid {
          overflow-y: auto;
          -webkit-overflow-scrolling: touch;

          &:deep(.game-item) {
            margin-bottom: 20px;

            .game-item-img {
              height: 110px !important;
            }

            .game-item-like {
              right: 6px;
              bottom: 6px;
            }
          }
        }

        &::-webkit-scrollbar {
          width: 0;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.1);
          border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: rgba(0, 0, 0, 0.2);
        }
      }

      .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px 20px;
        text-align: center;

        .clear-filters-btn {
          margin-top: 16px;
          cursor: pointer;
          background-color: #eee;
          display: inline-block;
          padding: 6px 10px;
          border-radius: 20px;
        }
      }
    }
  }
}
</style>
