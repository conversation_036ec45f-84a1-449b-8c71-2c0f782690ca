# AutoResizeText 组件

一个可以根据文本长度自动调整字体大小的 Vue 3 组件，保持容器宽度不变。

## 功能特点

- 🎯 **固定容器尺寸**：容器宽度和高度保持不变
- 📏 **动态字体大小**：根据文本长度自动调整字体大小
- 🎨 **高度可定制**：支持自定义样式、字体范围、容器尺寸等
- 🔄 **响应式更新**：文本变化时自动重新计算字体大小
- 🎪 **插槽支持**：支持使用插槽自定义内容
- 📱 **移动端友好**：适用于移动端 H5 应用
- 🚀 **零依赖**：纯原生 JavaScript 实现

## 基础用法

```vue
<template>
  <AutoResizeText
    :text="buttonText"
    :container-width="92"
    :container-height="36"
    container-class="my-button"
    @click="handleClick"
  />
</template>

<script setup>
import AutoResizeText from '@/components/common/AutoResizeText.vue'

const buttonText = ref('Download')

const handleClick = () => {
  console.log('Button clicked!')
}
</script>

<style>
.my-button {
  background: #007bff;
  color: white;
  border-radius: 6px;
  cursor: pointer;
}
</style>
```

## Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `text` | `string` | `""` | 要显示的文本内容 |
| `maxFontSize` | `number` | `14` | 最大字体大小（px） |
| `minFontSize` | `number` | `8` | 最小字体大小（px） |
| `containerWidth` | `number` | `92` | 容器宽度（px） |
| `containerHeight` | `number` | `36` | 容器高度（px） |
| `padding` | `number` | `10` | 容器内边距（px） |
| `fontWeight` | `string \| number` | `"700"` | 字体粗细 |
| `fontFamily` | `string` | `"inherit"` | 字体族 |
| `containerClass` | `string` | `""` | 容器的 CSS 类名 |
| `containerStyle` | `Record<string, any>` | `{}` | 容器的内联样式 |
| `disabled` | `boolean` | `false` | 是否禁用自动调整 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `click` | `event: MouseEvent` | 点击容器时触发 |
| `fontSizeChanged` | `fontSize: number` | 字体大小变化时触发 |

## 插槽

### 默认插槽

可以使用插槽来自定义显示内容：

```vue
<AutoResizeText :container-width="100" :container-height="40">
  <span>🎉 {{ customText }}</span>
</AutoResizeText>
```

## 暴露的方法

| 方法名 | 返回值 | 说明 |
|--------|--------|------|
| `adjustFontSize()` | `void` | 手动触发字体大小调整 |
| `getCurrentFontSize()` | `number` | 获取当前字体大小 |

### 使用暴露的方法

```vue
<template>
  <AutoResizeText ref="autoResizeRef" :text="text" />
  <button @click="manualAdjust">手动调整</button>
</template>

<script setup>
import { ref } from 'vue'

const autoResizeRef = ref()

const manualAdjust = () => {
  autoResizeRef.value?.adjustFontSize()
  console.log('Current font size:', autoResizeRef.value?.getCurrentFontSize())
}
</script>
```

## 高级用法

### 自定义样式

```vue
<AutoResizeText
  :text="text"
  :container-width="150"
  :container-height="50"
  :max-font-size="18"
  :min-font-size="10"
  font-weight="600"
  font-family="Arial, sans-serif"
  container-class="custom-button"
  :container-style="{ boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }"
  @font-size-changed="onFontSizeChanged"
/>
```

### 响应字体大小变化

```vue
<script setup>
const onFontSizeChanged = (fontSize) => {
  console.log(`Font size changed to: ${fontSize}px`)
  
  // 可以根据字体大小做一些额外的处理
  if (fontSize <= 10) {
    console.log('Text is getting quite small!')
  }
}
</script>
```

## 注意事项

1. **容器尺寸**：确保设置合适的容器宽度和内边距，为文本留出足够空间
2. **字体范围**：合理设置最大和最小字体大小，避免文本过大或过小
3. **性能考虑**：组件会在文本变化时重新计算，对于频繁变化的文本建议适当防抖
4. **样式继承**：字体族默认继承父元素，可以通过 `fontFamily` 属性自定义

## 实际应用场景

- 下载按钮文本自适应
- 导航菜单项文本适配
- 卡片标题自动缩放
- 移动端按钮文本优化
- 响应式标签文本

## 浏览器兼容性

支持所有现代浏览器，包括：
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
