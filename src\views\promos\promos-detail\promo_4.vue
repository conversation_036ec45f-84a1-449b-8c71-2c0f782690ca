<template>
  <XPage backgroundColor="#FFF" :narBarStyle="{ background: '#fff', color: '#000' }">
    <template #right>
      <van-icon
        @click="() => router.push('/account/transactions/Reward')"
        name="completed-o"
        size="24"
      />
    </template>
    <div class="promo4-bg">
      <div class="head-tabs">
        <button @click="activeTab = '1'" :class="[`head-tab`, { active: activeTab === '1' }]">
          Betted Today
        </button>
        <button @click="activeTab = '2'" :class="[`head-tab`, { active: activeTab === '2' }]">
          Yesterday Cashback
        </button>
      </div>
      <div class="cashback-content">
        <van-row class="jili-cashback">
          <!-- head -->
          <van-col span="18" class="cashback-title">JILI Games Cashback</van-col>
          <van-col span="6" class="cashback-title"
            >₱{{
              formatNumberToThousands(pageData.head[activeTab].cash_back, { precision: 2 })
            }}</van-col
          >
          <!-- body title -->
          <van-col span="5" class="body-title" style="text-indent: 6px">Provider</van-col>
          <van-col span="14" class="body-title" style="line-height: 1.5"
            >Betted {{ activeTab === "1" ? "Today" : "Yesterday(Cashback%)" }}</van-col
          >
          <van-col span="5" class="body-title">Cashback</van-col>
          <!-- body content -->
          <van-col span="5" class="body-content">JILI</van-col>
          <van-col span="14" class="body-content"
            >₱{{ pageData.head[activeTab].total_bet }}
            <span v-show="activeTab === '2'">(1.0%)</span>
          </van-col>
          <van-col span="5" class="body-content cashback"
            >₱{{ pageData.head[activeTab].cash_back }}</van-col
          >
        </van-row>
        <van-row class="types-cashback">
          <!-- head -->
          <van-col span="18" class="cashback-title">Cashback </van-col>
          <van-col span="6" class="cashback-title"
            >₱{{
              formatNumberToThousands(pageData.head[activeTab].cash_back, { precision: 2 })
            }}</van-col
          >
          <!-- body title-->
          <van-col span="5" class="body-title" style="text-indent: 6px">Category</van-col>
          <van-col span="14" class="body-title" style="line-height: 1.5"
            >Betted {{ activeTab === "1" ? "Today(valid bet)" : "Yesterday(Cashback%)" }}</van-col
          >
          <van-col span="5" class="body-title">Cashback</van-col>
          <!-- body content -->
          <template v-for="row in pageData.content[activeTab].data" :key="row.category">
            <van-col span="5" class="body-content">{{ row.category }}</van-col>
            <van-col span="14" class="body-content"
              >₱{{ row.bet_amount }}
              <span v-show="activeTab == '2'">({{ row.cashback }})</span>
            </van-col>
            <van-col span="5" class="body-content cashback"
              >₱{{
                ((Number(row.cashback.replace("%", "")) / 100) * Number(row.bet_amount)).toFixed(0)
              }}</van-col
            >
          </template>
        </van-row>
      </div>
      <div class="vip-benefit-section">
        <h2>Benefit</h2>
        <table class="vip-table">
          <tr>
            <th>Game Provider</th>
            <th>VIP CashBack %</th>
          </tr>
          <tr>
            <td>Jili Games</td>
            <td>1.0%</td>
          </tr>
        </table>
        <table class="vip-table">
          <tr>
            <th>Game Provider</th>
            <th>VIP CashBack %</th>
          </tr>
          <tr>
            <td>Other Games</td>
            <td>0.8%</td>
          </tr>
        </table>
        <h2>GENERAL MECHANICS</h2>
        <ol class="vip-list">
          <li>
            From 00:00:01 AM to 11:59:59 PM, NUSTAR VIP members will receive 1.0% daily cashback on
            valid bets placed on any Jili games, and 0.8% cashback on valid bets placed on other
            games.
          </li>
          <li>Bonuses will be credited to accounts by 1:00 PM the following day.</li>
          <li>No wagering requirements are necessary for withdrawal.</li>
        </ol>
        <h2>HOW TO BECOME A VIP</h2>
        <ol class="vip-list">
          <li>
            Achieve ₱250,000 in bets within a half-month to qualify. Periods are from 1st-15th and
            16th-end of the month.
          </li>
          <li>Maintain VIP status with ₱300,000 in bets each half-month.</li>
          <li>
            VIP status updates occur automatically on the 1st and 16th. NUSTAR VIP members not
            meeting the criteria will be downgraded.
          </li>
        </ol>
      </div>
      <ZFootPng background="linear-gradient(90deg,#fde5cb,#4e2801 80%)" />

      <!-- Footer Button -->
      <div class="promo4-footer">
        <button
          class="promo4-btn"
          @click="() => router.replace('/game-categories?categoryId=history')"
        >
          Go Bet
        </button>
      </div>
    </div>
  </XPage>
</template>

<script setup>
import { useRouter } from "vue-router";
import { getGlobalDialog } from "@/enter/vant";
import { useGlobalStore } from "@/stores/global";
import { rebateConf } from "@/api/user";
import { dailyToday, dailyYesterday, cashback } from "@/api/promos";
import { formatNumberToThousands } from "@/utils/core/tools";

const router = useRouter();
const dialog = getGlobalDialog();
const globalStore = useGlobalStore();
const isVip = ref(globalStore.userInfo.is_vip == 1);

const fixedCashback = { cash_back: "0", total_bet: "0.00" };
const activeTab = ref("1"); // 默认1 1 today   2 yesterday
const pageData = ref({ head: { 1: fixedCashback, 2: fixedCashback }, content: { 1: {}, 2: {} } });

const getData = async () => {
  // 判断是否vip,查询vipcashback 非vip则默认0
  const res = await Promise.all([
    ...(isVip.value
      ? [cashback({ type: 1 }), cashback({ type: 2 })]
      : [fixedCashback, fixedCashback]),
    rebateConf(),
    dailyToday(),
    dailyYesterday(),
  ]);
  pageData.value = {
    head: {
      1: res[0],
      2: res[1],
    },
    content: {
      1: { data: res[3] },
      2: res[4],
    },
  };

  console.log("getData", res);
};
const initdata = async () => {
  getData();
  // 非vip弹窗，点击done跳转 vip 说明详情
  const hasVisitVipIntro = localStorage.getItem("hasVisitVipIntro");
  if (!isVip.value && !hasVisitVipIntro) {
    dialog({
      title: "Tips",
      message: "Qualified VIP members will enjoy 0.8% cashback",
      showCancelButton: false,
      confirmText: "Done",
      onConfirm: async () => {
        router.push({ path: "/account/vip-introduction", query: { source: "vipCashback" } });
      },
    });
  }
};

onMounted(() => {
  initdata();
});
</script>

<style lang="scss" scoped>
.promo4-bg {
  color: #fff;
  min-height: 100%;
  position: relative;
  background-color: #070a11;
  background-image: url("@/assets/images/promos/promo_4.png");
  background-repeat: no-repeat;
  background-size: contain;
  background-position: top 10px left 0;
  padding-bottom: 70px;

  .head-tabs {
    width: 100%;
    display: flex;
    font-size: 16px;
    justify-content: space-between;
    padding: 310px 14px 0;
    gap: 10px;

    .head-tab {
      flex: 1;
      padding: 8px 0;
      text-align: center;
      border-radius: 24px;
      border: 1.6px solid #ac8b72;
      background: linear-gradient(-120deg, #7b5d45 0%, #402205 90%);
      color: #c2926a;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
      outline: none;
    }

    .head-tab.active {
      background: linear-gradient(-170deg, #b28763, #5c3308 70%);
      color: #fff;
      border-color: #f4c7a2;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.18);
    }
  }

  .cashback-content {
    padding: 18px 20px 60px;
    line-height: 40px;
    font-size: 14px;

    .cashback-title {
      font-size: 18px;
      font-weight: 600;
    }

    .body-title,
    .body-content {
      // line-height: 15.4vw;
      line-height: 4.1;
    }

    .body-title {
      padding: 0 !important;
      color: #ffe3c2;
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }

    &:deep(.van-col) {
      padding: 0 10px;
    }

    .body-content {
      &.cashback {
        text-align: right;
      }
    }

    .jili-cashback {
      margin-bottom: 5.2vw;
    }
  }
}

.vip-benefit-section {
  background: linear-gradient(90deg, #010004, #271401);
  margin: 0 auto;
  padding: 1px 20px;
  color: #f5e7d0;

  h2 {
    font-size: 16px;
    font-weight: 500;
    margin: 20px 0 18px;
    color: #f5e7d0;
    letter-spacing: 1px;
  }

  .vip-table {
    width: 100%;
    border: 1px solid #a88c6b;
    border-radius: 8px;
    margin-bottom: 18px;
    background: rgba(0, 0, 0, 0.15);
    border-collapse: separate;
    border-spacing: 0;

    th,
    td {
      padding: 10px 0;
      text-align: center;
      font-size: 16px;
      border-bottom: 1px solid #a88c6b;

      &:first-child {
        border-right: 1px solid #a88c6b;
      }
    }

    th {
      font-weight: bold;
      color: #f5e7d0;
      background: none;
      border-top: none;
    }

    tr:last-child td {
      border-bottom: none;
    }
  }

  .vip-list {
    letter-spacing: normal;
    list-style: auto;
    margin-left: 18px;
    margin-bottom: 18px;
    line-height: 2;

    li {
      margin-bottom: 10px;
      font-size: 14px;
      line-height: 1.7;
      color: #f5e7d0;
    }
  }
}

.footer-bar {
  width: 100%;
  background: #271401;
  padding: 10px 0 80px;
  margin-top: -1px;

  img {
    width: 100%;
  }
}

.promo4-footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(248, 251, 248, 0.2);
  border-radius: 20px 20px 0 0;
  padding: 8px 0 12px;
  display: flex;
  justify-content: center;
  z-index: 10;
}

.promo4-btn {
  width: 90%;
  background: linear-gradient(90deg, #573109 20%, #b18662 100%);
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  border: none;
  border-radius: 50px;
  padding: 10px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}
</style>
