<template>
  <ZPage :request="getList" backgroundColor="#F4F8FB" :narBarStyle="{ background: 'transparent' }">
    <div class="mainbox">
      <div class="combine-head">
        <div class="title">Withdrawal(₱)</div>
        <div class="amount">
          {{ combineOrder.suc_amount || "--" }}/{{ combineOrder.total_amount || "--" }}
        </div>
        <div class="order-no">Combine OrderID:{{ combineOrder.combine_order_no }}</div>
      </div>

      <div class="combine-list">
        <!-- 列表 -->
        <van-list
          v-model:loading="loading"
          :finished="combineOrder.total_page <= combineOrder.current_page || loading"
          loading-text="Loading..."
          @load="onLoad"
        >
          <div class="items-wrap">
            <div v-for="it in combineList" :key="it.id" class="items-content">
              <!-- 行内容 -->
              <LineSetting
                valueStatus="normal"
                :valueStyle="{
                  color: STATUS_COLORS[it.status_name] || '#999',
                }"
                :text="it.title"
                :value="it.status_name"
                :showArrow="false"
                :rightStyle="{ fontWeight: 600 }"
                :rightText="it.right_amount"
                @click="jumpDetail(it)"
              >
                <template #icon>
                  <div class="icon-img">
                    <span
                      :class="[
                        `gift`,
                        {
                          Maya: it.payment_method
                            ? it.payment_method.toLowerCase().includes('maya')
                            : false,
                          Gcash: it.payment_method
                            ? it.payment_method.toLowerCase().includes('gcash')
                            : false,
                        },
                      ]"
                    ></span>
                  </div>
                </template>
              </LineSetting>
            </div>
          </div>
        </van-list>
      </div>
    </div>
  </ZPage>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from "vue-router";
import { ref, onBeforeMount, watch } from "vue";
import LineSetting from "@/views/account/components/LineSetting.vue";
import { getCombineOrderList } from "@/api/user";
import { processTransactionStatus, STATUS_COLORS } from "./config";

// const mockItem = {
//   id: "104322853129646085",
//   payment_method: "Maya",
//   order_no: "*****************",
//   account_no: "",
//   total_amount: 0,
//   remark: "1",
//   paid_at: "2025-06-25 14:36:09",
//   ship_status: 2,
//   audit_status: 2,
//   created_at: "2025-06-25 14:36:09",
//   status_desc: "Successful",
//   sys_remark: "Adjustment",
//   ship_fail_code: 0,
//   ship_fail_message: "",
//   err_msg: "",
// };

// // 生成指定长度的数组
// function generateArray(length, defaultValue = {}) {
//   return Array.from({ length }, () => defaultValue);
// }

// // 示例：生成长度为5的数组，默认值为0
// const arr1 = generateArray(5, mockItem).map((p) => processTransactionStatus(p, "Withdrawal"));

const MERGE_ITEM_CONSTANT = {
  // 分页、总计金额等信息
  suc_amount: 0,
  total_amount: 0,
  combine_order_no: "--",
  count: 0,
  current_page: 0,
  total_page: 0,
};

interface orderInfo {
  suc_amount: number;
  total_amount: number;
  combine_order_no: string;
  count: number;
  current_page: number;
  total_page: number;
}

const router = useRouter();
const route = useRoute();

const { id } = route.params;
const loading = ref(false);
const combineList = ref([]); // 组合订单列表
const combineOrder = ref<orderInfo>(MERGE_ITEM_CONSTANT); //存储页码、金额等信息内容

// 跳转详情
const jumpDetail = (data = {}) => {
  router.push({ path: "/account/transactions/detail", query: data });
};

const getList = async () => {
  if (combineOrder.value.current_page >= combineOrder.value.total_page) return;
  loading.value = true;
  const curPage = combineOrder.value.current_page + 1;
  const res = await getCombineOrderList(
    {
      page: curPage,
      page_number: 15,
    },
    id
  );
  if (!res.data) return;
  const { list, suc_amount, total_amount, ...ret } = res.data;
  const newList = combineList.value.concat(
    list.map((t) => processTransactionStatus(t, "Withdrawal"))
  );
  combineList.value = newList;
  combineOrder.value = {
    ...ret,
    suc_amount: suc_amount / 100,
    total_amount: total_amount / 100,
  };
  loading.value = false;
};

// 加载更多
const onLoad = async () => {
  if (loading.value) return;
  // 防止快速滚动时多次触发
  loading.value = true; // 提前标记加载中
  try {
    await getList();
  } catch (e) {
    console.error(`加载更多数据失败`, e);
  } finally {
    loading.value = false;
  }
};
</script>
<style lang="scss" scoped>
.mainbox {
  .combine-head {
    background: #fff;
    margin: 0 12px 16px;
    border-radius: 12px;
    text-align: center;
    padding: 10px;
    .amount {
      font-size: 30px;
      font-weight: 700;
      margin-bottom: 18px;
    }
    .order-no {
      color: #999;
    }
  }

  .combine-list {
    margin: 0 12px;
    background: #fff;
    padding: 10px;
    border-radius: 12px;
  }

  .items-wrap {
    background: #fff;
    border-radius: 10px;
  }
  .items-content {
    padding: 0 12px;

    .icon-img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #f9f9f9;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .gift {
      display: inline-block;
      overflow: hidden;
      width: 28px;
      height: 28px;
      background-image: url(/src/assets/images/account/coin_img.png);
      background-repeat: no-repeat;
      background-size: 100px 55px;
      background-position: left -73px top -2px;

      &.Maya {
        background-position: left -19vw top -7.6vw;
      }

      &.Gcash {
        background-position: left -0.5vw top -2px;
      }
    }
  }
}
</style>
