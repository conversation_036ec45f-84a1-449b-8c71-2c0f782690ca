<template>
  <div v-if="showDownloadGuide" class="download-guide-container">
    <div class="download-guide-content">
      <div v-if="canClose" class="close-btn" @click="handleClose">
        <ZIcon type="icon-guanbi1" color="#fff" :size="16" />
      </div>
      <div class="app-info">
        <img
          :src="downloadConfig.icon ? getServerSideImageUrl(downloadConfig.icon) : defaultAppIcon"
          alt="App Icon"
          class="app-icon"
        />
        <div class="app-details">
          <div v-if="downloadConfig.slogan" class="app-slogan">
            {{ downloadConfig.slogan }}
          </div>
        </div>
      </div>
      <div class="download-btn" ref="fittyElement" @click="handleDownload">
        {{ downloadConfig.button_text || "Download" }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { storeToRefs } from "pinia";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { MobileWindowManager } from "@/utils/managers/MobileWindowManager";
import { getServerSideImageUrl } from "@/utils/core/tools";
import fitty from "fitty";

// 默认应用图标 - 使用favicon
const defaultAppIcon = "/favicon.ico";

// Store引用
const autoPopMgrStore = useAutoPopMgrStore();
const { downloadGuideConfig } = storeToRefs(autoPopMgrStore);
// 本地状态
const showDownloadGuide = ref(false);

// 计算属性：下载配置
const downloadConfig = computed(() => {
  return downloadGuideConfig.value || {};
});

const fittyElement = ref(null);
import type { FittyInstance } from "fitty";
let fittyInstance: FittyInstance | null = null;

// 计算属性：是否可以关闭
const canClose = computed(() => {
  console.log("计算属性：是否可以关闭", downloadConfig.value?.close_type);
  // close_type: 1-允许关闭, 2-不允许关闭
  return downloadConfig.value?.close_type !== 2;
});

// 检查是否应该显示下载引导
const shouldShowDownloadGuide = () => {
  return !!downloadGuideConfig.value?.download_url;
};

// 处理关闭
const handleClose = () => {
  // 只有在允许关闭时才能关闭
  if (canClose.value) {
    showDownloadGuide.value = false;
  }
};

// 处理下载
const handleDownload = () => {
  const downloadUrl = downloadConfig.value?.download_url;

  if (downloadUrl) {
    // 使用MobileWindowManager处理下载链接
    const success = MobileWindowManager.navigateToUrl(downloadUrl);
    if (!success) {
      console.error("Failed to open download URL:", downloadUrl);
    }
  } else {
    console.warn("Download URL not configured");
  }

  // 下载后关闭提示 close_type : 1 可关闭 2 不可关闭
  showDownloadGuide.value = downloadConfig.value?.close_type === 1 ? false : true;
};

// 初始化显示逻辑
const initDownloadGuide = async () => {
  // 先获取配置
  await autoPopMgrStore.getDownloadGuideConfig();

  // 如果后台配置启用，则显示
  if (shouldShowDownloadGuide()) {
    showDownloadGuide.value = true;
  }
};

// 组件挂载时初始化
onMounted(() => {
  initDownloadGuide();
  if (fittyElement.value) {
    fittyInstance = fitty(fittyElement.value, {
      minSize: 8,
      maxSize: 14,
      multiLine: true,
    });
  }
});

watch(
  () => downloadConfig.value.button_text,
  () => {
    if (fittyInstance) {
      fittyInstance.fit();
    }
  }
);

// 暴露方法供外部调用
defineExpose({
  show: () => {
    showDownloadGuide.value = true;
  },
  hide: () => {
    showDownloadGuide.value = false;
  },
});
</script>

<style lang="scss" scoped>
.download-guide-container {
  position: fixed;
  bottom: 80px; /* 避免遮挡底部导航栏，TabBar高度54px + 安全区域 */
  left: 12px;
  right: 12px;
  z-index: 999; /* 低于弹窗组件的z-index */
  background: #ac1140;
  padding: 8px 12px;
  border-radius: 12px; /* 添加圆角 */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.3s ease-out;
  font-family: "Inter";
  height: 70px;
  width: 94vw;
  overflow: hidden;
}

.download-guide-content {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.close-btn {
  position: absolute;
  top: -10px;
  right: -12px;
  width: 16px;
  height: 16px;
}

.app-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.app-icon {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  object-fit: cover;
}

.app-details {
  flex: 1;
  color: white;
}

.app-title {
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 2px;
}

.app-description {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.3;
}

.app-slogan {
  font-size: 11px;
  font-weight: 400;
  margin-top: 2px;
  // height: 55px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: min(3vw, 5vw);
  text-align: left;

  display: -webkit-box;
  display: -moz-box;
  display: box;
  -webkit-line-clamp: 3;
  -moz-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical;
  box-orient: vertical;
  overflow: hidden;
}

.download-btn {
  background: #fff;
  color: #ac1140;
  width: 92px;
  height: 36px;
  padding: 0 8px;
  border-radius: 100px;
  font-weight: 700;
  white-space: nowrap;
  transition: all 0.2s ease;
  // font-size: min(2vw, 14px);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: pre-wrap;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
