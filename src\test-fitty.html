<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fitty Test</title>
    <script src="https://unpkg.com/fitty@2.4.2/dist/fitty.min.js"></script>
    <style>
        .test-container {
            width: 92px;
            height: 36px;
            background: #fff;
            color: #ac1140;
            border-radius: 100px;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px;
            border: 2px solid #ac1140;
            overflow: hidden;
        }
        
        .controls {
            margin: 20px;
        }
        
        button {
            margin: 5px;
            padding: 10px;
        }
    </style>
</head>
<body>
    <h1>Fitty Test Page</h1>
    
    <div class="controls">
        <button onclick="changeText('Download')">Short Text</button>
        <button onclick="changeText('Download Now')">Medium Text</button>
        <button onclick="changeText('Download Application')">Long Text</button>
        <button onclick="changeText('Download Application Now')">Very Long Text</button>
    </div>
    
    <div class="test-container" id="fittyElement">
        Download
    </div>
    
    <script>
        let fittyInstance = null;
        
        function initFitty() {
            const element = document.getElementById('fittyElement');
            if (element) {
                if (fittyInstance) {
                    fittyInstance.unsubscribe();
                }
                
                fittyInstance = fitty(element, {
                    minSize: 8,
                    maxSize: 14,
                    multiLine: false,
                });
                
                console.log('Fitty initialized:', fittyInstance);
            }
        }
        
        function changeText(text) {
            const element = document.getElementById('fittyElement');
            element.textContent = text;
            
            if (fittyInstance) {
                fittyInstance.fit();
            }
        }
        
        // Initialize on page load
        window.addEventListener('load', () => {
            setTimeout(initFitty, 100);
        });
    </script>
</body>
</html>
