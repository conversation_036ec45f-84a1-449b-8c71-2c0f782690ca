import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { getSessionStorage, setSessionStorage } from "@/utils/core/Storage";
import { KycMgr, InGameType } from "@/utils/KycMgr";

export const enum EnumPopUpPrefabURL {
  Tip21Old,
  Envelope, // 首存红包奖励弹窗
  VipTip, // 升级VIP提示
  PopupBanners,
  RegisterBonus, // 注册/绑定奖励
  ActivityBonus, // 活动奖励
  // KYCPop, // kyc 弹窗
  LeaderBoardPop, // 排行弹窗
  LeaderBoardPopJILI, // JILI 排行弹窗
  SpinWheel, // 转盘
}

// 优先级配置（优先级越高越先弹出）
const HallAutoPopConfig: Record<EnumPopUpPrefabURL, { priority: number }> = {
  // [EnumPopUpPrefabURL.KYCPop]: { priority: 15 }, // 优先级最大，弹窗单独拎出来了
  [EnumPopUpPrefabURL.Tip21Old]: { priority: 14 },
  [EnumPopUpPrefabURL.Envelope]: { priority: 13 },
  [EnumPopUpPrefabURL.VipTip]: { priority: 12 },
  [EnumPopUpPrefabURL.ActivityBonus]: { priority: 11 },
  [EnumPopUpPrefabURL.PopupBanners]: { priority: 10 },
  [EnumPopUpPrefabURL.LeaderBoardPop]: { priority: 9 },
  [EnumPopUpPrefabURL.LeaderBoardPopJILI]: { priority: 8 },
  [EnumPopUpPrefabURL.SpinWheel]: { priority: 7 },
  [EnumPopUpPrefabURL.RegisterBonus]: { priority: 5 },
};

// 根据 popup 类型检查是否满足显示条件
function isPopupEligible(type: EnumPopUpPrefabURL): boolean {
  const autoPopMgrStore = useAutoPopMgrStore();
  switch (type) {
    case EnumPopUpPrefabURL.VipTip:
      return autoPopMgrStore.isNeedShowVipTip();
    case EnumPopUpPrefabURL.Tip21Old:
      return autoPopMgrStore.isNeedShowTip21Old();
    case EnumPopUpPrefabURL.ActivityBonus:
      return autoPopMgrStore.isNeedShowActivityBonus();
    case EnumPopUpPrefabURL.RegisterBonus:
      return autoPopMgrStore.isNeedShowRegisterBonus();
    case EnumPopUpPrefabURL.Envelope:
      return autoPopMgrStore.isNeedShowEnvelopePop();
    case EnumPopUpPrefabURL.PopupBanners:
      return autoPopMgrStore.isNeedShowPopupBanner();
    case EnumPopUpPrefabURL.LeaderBoardPop:
      return autoPopMgrStore.isNeedShowRankPop();
    case EnumPopUpPrefabURL.LeaderBoardPopJILI:
      return autoPopMgrStore.isNeedShowRankPopJili();
    case EnumPopUpPrefabURL.SpinWheel:
      return autoPopMgrStore.isNeedShowSpin();
    // case EnumPopUpPrefabURL.KYCPop:
    //   return autoPopMgrStore.isNeedShowPopKYC();
    default:
      return false;
  }
}

export const AutoPopMgr = (function () {
  let isShowing = false;
  let shownPopups = new Set<EnumPopUpPrefabURL>(); // 已显示或处理过的弹窗

  // 获取所有满足条件且未显示的弹窗（按优先级排序）
  function getAvailablePopups(): EnumPopUpPrefabURL[] {
    return Object.keys(HallAutoPopConfig)
      .map((key) => Number(key) as EnumPopUpPrefabURL)
      .filter((type) => !shownPopups.has(type) && isPopupEligible(type))
      .sort((a, b) => HallAutoPopConfig[b].priority - HallAutoPopConfig[a].priority);
  }

  // 获取下一个待显示的弹窗
  function getNextPopup(): EnumPopUpPrefabURL | null {
    const available = getAvailablePopups();
    return available.length > 0 ? available[0] : null;
  }

  // 辅助函数：根据传入的 eligibility 判断执行对应的显示操作
  function showPopupWithEligibility(eligibility: boolean, showAction: () => void) {
    if (eligibility) {
      showAction();
    } else {
      AutoPopMgr.destroyCurrentPopup();
    }
  }

  return {
    // 自动弹出弹窗：如果当前没有弹窗显示，则获取下一个弹窗并展示
    autoPopupDialog() {
      if (isShowing) return;

      const nextPopup = getNextPopup();
      if (nextPopup !== null) {
        console.log("AutoPopMgr: 准备显示弹窗类型:", nextPopup);
        this.showPopup(nextPopup);
      }
    },

    // 判断是否还有待显示弹窗
    hasPendingPopups(): boolean {
      return getAvailablePopups().length > 0;
    },

    // 根据类型显示弹窗
    showPopup(popType: EnumPopUpPrefabURL) {
      const autoPopMgrStore = useAutoPopMgrStore();
      isShowing = true;
      shownPopups.add(popType); // 标记为已处理过
      // 每次添加后保存状态
      switch (popType) {
        case EnumPopUpPrefabURL.VipTip:
          showPopupWithEligibility(autoPopMgrStore.isNeedShowVipTip(), () => {
            autoPopMgrStore.showVipTip = true;
          });
          break;
        case EnumPopUpPrefabURL.Tip21Old:
          showPopupWithEligibility(autoPopMgrStore.isNeedShowTip21Old(), () => {
            autoPopMgrStore.showTips21Tip = true;
            window["showTip21Old"] = true;
          });
          break;
        case EnumPopUpPrefabURL.ActivityBonus:
          showPopupWithEligibility(autoPopMgrStore.isNeedShowActivityBonus(), () => {
            autoPopMgrStore.showActivityBonusTip = true;
          });
          break;
        case EnumPopUpPrefabURL.PopupBanners:
          showPopupWithEligibility(autoPopMgrStore.isNeedShowPopupBanner(), () => {
            autoPopMgrStore.showPopupBannersTip = true;
          });
          break;
        case EnumPopUpPrefabURL.LeaderBoardPop:
          showPopupWithEligibility(autoPopMgrStore.isNeedShowRankPop(), () => {
            autoPopMgrStore.showLeaderBoardPopTip = true;
          });
          break;
        case EnumPopUpPrefabURL.LeaderBoardPopJILI:
          showPopupWithEligibility(autoPopMgrStore.isNeedShowRankPopJili(), () => {
            autoPopMgrStore.showLeaderBoardPopJILITip = true;
          });
          break;
        case EnumPopUpPrefabURL.RegisterBonus:
          showPopupWithEligibility(autoPopMgrStore.isNeedShowRegisterBonus(), () => {
            autoPopMgrStore.showRegisterBonusTip = true;
          });
          break;
        case EnumPopUpPrefabURL.Envelope:
          showPopupWithEligibility(autoPopMgrStore.isNeedShowEnvelopePop(), () => {
            autoPopMgrStore.showEnvelopeTip = true;
          });
          break;
        case EnumPopUpPrefabURL.SpinWheel:
          showPopupWithEligibility(autoPopMgrStore.isNeedShowSpin(), () => {
            autoPopMgrStore.showSpinWheelTip = true;
          });
        // case EnumPopUpPrefabURL.KYCPop:
        // let self = this;
        // KycMgr.instance.verifyKyc(InGameType.Login, (isVerity) => {
        //   console.log("isVerity", isVerity);
        //   if (isVerity) {
        //     this.destroyCurrentPopup(); //直接向下执行
        //   } else {
        //     setTimeout(() => {
        //       if (!KycMgr.instance.poping) {
        //         self.destroyCurrentPopup(); //直接向下执行
        //       }
        //     }, 100);
        //   }
        // });
        // showPopupWithEligibility(autoPopMgrStore.isNeedShowPopKYC(), () => {
        //   autoPopMgrStore.showKycTip = true;
        // });
        default:
          isShowing = false;
          break;
      }
    },

    // 销毁当前弹窗并在 200 毫秒后尝试弹出下一个弹窗
    destroyCurrentPopup() {
      isShowing = false;
      setTimeout(() => {
        this.autoPopupDialog();
      }, 200);
    },
    // 重置所有弹窗状态（主要用于测试或重置逻辑）
    resetAllPopups() {
      shownPopups.clear();
      isShowing = false;
    },
    // 可扩展：提供一个恢复状态方法，在 Home.vue mounted 时调用
    restoreState() {},
  };
})();
