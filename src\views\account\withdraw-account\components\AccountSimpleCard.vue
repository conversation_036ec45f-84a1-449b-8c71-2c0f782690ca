<template>
  <div class="card-wrap" :style="{ background: cardInfo.backgroundColor }" @click="handleCheck">
    <div class="card-logo">
      <WithdrawTypeIcon :icon="item.icon" />
    </div>
    <span class="card-name">{{ cardInfo.name }}</span>
    <div class="check-wrap">
      <CheckedUnCheckedIcon
        :type="METHODS_NAMES[item.account_type]"
        :isChecked="checkedType.toLocaleLowerCase() === item.name.toLocaleLowerCase()"
      >
      </CheckedUnCheckedIcon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from "vue";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";
import { Item } from "./type";
import { getMethodsInfo, METHODS_NAMES } from "@/utils/config/GlobalConstant";

const emits = defineEmits(["check"]);

const props = defineProps({
  checkedType: {
    type: String as () => CHANEL_TYPE,
    required: false,
    default: CHANEL_TYPE.MAYA,
    validator: (value: string) => {
      return Object.values(CHANEL_TYPE).includes(value as CHANEL_TYPE);
    },
  },
  item: {
    type: Object as () => Item,
    required: false,
    default: () => {},
  },
});

const cardInfo = computed(() => {
  const base = getMethodsInfo(props.item.account_type);
  return {
    name: base.name,
    backgroundColor: base.activeColor,
  };
});

const handleCheck = () => {
  emits("check", props.item);
};
</script>

<style scoped lang="scss">
.card-wrap {
  width: 100%;
  height: 68px;
  border-radius: 20px;
  color: #fff;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  padding: 18px 12px;
  position: relative;

  .card-name {
    margin-left: 8px;
    color: #fff;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .check-wrap {
    position: absolute;
    right: 12px;
    bottom: 6px;
  }
}
</style>
