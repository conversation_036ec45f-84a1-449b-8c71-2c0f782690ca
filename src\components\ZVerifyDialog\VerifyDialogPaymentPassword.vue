<!-- 支付密码设置弹窗 -->
<template>
  <ZActionSheet
    v-model="visible"
    title="Set Payment Password"
    :showCancelButton="false"
    :showConfirmButton="false"
    :onConfirm="handleCompletePayPwd"
    :onCancel="handleCancel"
  >
    <div class="dialog-content">
      <div class="phone-input">
        <div class="set-password-tip">
          <div v-show="dialogStep === 1">Set your payment password</div>
          <div v-show="dialogStep === 2">Re-enter the same payment password</div>
          <div class="tip2">
            We do not store user information. Please take care not to lose access.
          </div>
        </div>
        <ZPasswordInput
          v-if="dialogStep === 1"
          v-model="paymentPassword"
          :visible="visible"
          @complete="handleCompletePayPwd"
        />
        <ZPasswordInput
          v-else
          v-model="paymentPasswordRepeat"
          :visible="visible"
          :errInput="isPasswordNotSame"
          @complete="handleCompletePayPwd"
          @click="handleRepeat"
        />
        <div v-show="isPasswordNotSame" class="error-tip">
          The passwords entered are inconsistent
        </div>
      </div>
    </div>
  </ZActionSheet>
</template>

<script setup lang="ts">
import { GeetestMgr, GEETEST_TYPE } from "@/utils/GeetestMgr";
import { useGlobalStore } from "@/stores/global";
import { ref, onUnmounted } from "vue";
import { Md5 } from "@/utils/core/Md5";
import { withdrawPassword } from "@/api/user";
import { showToast } from "vant";

// 全局状态
const globalStore = useGlobalStore();
const userInfo = ref(globalStore.userInfo);

const props = defineProps({
  // 显示弹窗
  showNextDialog: {
    type: Boolean,
    default: false,
    required: false,
  },
  // 成功回调
  succCallBack: {
    type: Function,
    default: () => {},
    required: false,
  },
  // 成功提示
  toastText: {
    type: String,
    default: "Payment password set successfully",
    required: false,
  },
});

// 响应式数据
const dialogStep = ref(1); // 步骤：1-验证码 / 2-设密码 / 3-确认密码
const paymentPassword = ref(""); // 支付密码（第一次）
const paymentPasswordRepeat = ref(""); // 支付密码（第二次）
const isPasswordNotSame = ref(false); // 两次密码是否不一致
// 弹窗是否显示
const visible = ref(props.showNextDialog);
let countdownTimer: NodeJS.Timeout | null = null; // 倒计时定时器

watch(
  () => props.showNextDialog,
  (val) => {
    visible.value = val;
    if (!val) {
      resetData();
    }
  }
);

// 监听 visible 变化，同步到父组件
watch(
  () => visible.value,
  (val) => {
    if (val !== props.showNextDialog) {
      emit("update:showNextDialog", val);
    }
  }
);

const emit = defineEmits(["update:showNextDialog", "complete", "repeat", "close"]);

const handleCancel = () => {
  emit("update:showNextDialog", false);
};

const resetData = () => {
  dialogStep.value = 1;
  paymentPassword.value = "";
  paymentPasswordRepeat.value = "";
  isPasswordNotSame.value = false;
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
};

/**
 * 通用极验校验函数
 * @param loginType 极验类型
 * @param callback  校验成功后的回调
 */
const geetestValidate = (loginType: string, callback: Function) => {
  if (!loginType) return;

  GeetestMgr.instance.geetest_device(loginType, (successRes) => {
    if (successRes) {
      console.log("successRes", successRes);
      callback({
        geetest_guard: successRes.geetest_guard || "",
        userInfo: successRes.userInfo || "",
        geetest_captcha: successRes.geetest_captcha || "",
        buds: successRes.buds || "64",
      });
    }
  });
};

/**
 * 完成支付密码设置流程
 * 逻辑：
 *  1. 步骤1 → 校验验证码
 *  2. 步骤2 → 进入确认密码
 *  3. 步骤3 → 校验两次密码一致性并发起接口请求
 */
const handleCompletePayPwd = () => {
  if (dialogStep.value === 1) {
    // 步骤2：进入确认密码
    dialogStep.value = 2;
  } else if (dialogStep.value === 2) {
    // 步骤3：校验两次密码一致性
    if (paymentPassword.value !== paymentPasswordRepeat.value || !paymentPassword.value) {
      isPasswordNotSame.value = true;
      return;
    }
    isPasswordNotSame.value = false;
    // 极验 + 密码设置
    const requestType = userInfo.value.withdraw_password
      ? GEETEST_TYPE.change_pay_password
      : GEETEST_TYPE.first_pay_password;

    geetestValidate(requestType, (geetestData) => {
      withdrawPassword({
        withdraw_password: Md5.hashStr(paymentPassword.value).toString(),
        ...geetestData,
      })
        .then((res) => {
          globalStore.updateUserInfo({ withdraw_password: 1 }); // 更新全局状态
          props.toastText && showToast(props.toastText);
          handleCancel(); // 关闭当前弹窗
          emit("complete"); // 通知父组件完成
          props.succCallBack && props.succCallBack();
        })
        .catch((err) => {
          err?.msg && showToast(err?.msg);
        });
    });
  }
};

/**
 * 重复设置（如需）
 * 通知父组件触发重复逻辑
 */
const handleRepeat = () => {
  emit("repeat");
};

/**
 * 组件卸载时清除定时器
 * 防止内存泄漏
 */
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
});
</script>

<style scoped lang="scss">
.dialog-content {
  padding-bottom: 40px;

  .set-password-tip {
    color: #666;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 16px;

    .tip2 {
      margin-top: 10px;
      line-height: 1;
      color: #999;
    }

    /* 171.429% */
  }

  // 密码步骤样式
  .phone-input {
    .error-tip {
      color: #e5110a;
      font-size: 12px;
      margin-top: 4px;
    }
  }
}
</style>
