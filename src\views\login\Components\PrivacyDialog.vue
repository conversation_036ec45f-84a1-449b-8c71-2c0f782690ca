<!--
  隐私政策确认弹窗组件
-->
<template>
  <ZActionSheet v-model="dialogVisible" title="Privacy Agreement" :show-confirm-button="true"
    :show-cancel-button="false" :on-confirm="handleConfirm" :on-cancel="handleClose">
    <div class="privacy-content">
      <p>
        By logging in or registering, you confirm that you are over 21 years old, not listed in the
        PAGCOR NDRP, not a GEL licenseholder, and not a government employee. You also agree with the
        <span class="link-text" @click="handleNavigate('/protocal/terms-of-use')">Terms of use</span> and
        <span class="link-text" @click="handleNavigate('/protocal/privacy-policy')">Privacy Policy.</span>
      </p>
    </div>
  </ZActionSheet>
</template>

<script setup lang="ts">
/**
 * 隐私政策确认弹窗组件
 */
import { computed } from "vue";
import { useRouter } from "vue-router";

interface Props {
  /** 弹窗是否可见 */
  visible?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
});

const emit = defineEmits<{
  /** 更新可见状态 */
  'update:visible': [visible: boolean];
  /** 确认隐私协议 */
  confirm: [];
}>();

const router = useRouter();

/** 弹窗可见状态 */
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

/**
 * 处理确认按钮点击
 */
const handleConfirm = () => {
  emit('confirm');
  handleClose();
};

/**
 * 处理关闭弹窗
 */
const handleClose = () => {
  emit('update:visible', false);
};

/**
 * 处理页面导航
 */
const handleNavigate = (path: string) => {
  router.push(path);
  handleClose();
};
</script>

<style scoped lang="scss">
.privacy-content {
  p {
    margin: 0;
    color: #333;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;

    .link-text {
      color: #ac1140;
      font-weight: 500;
      cursor: pointer;
      text-decoration: underline;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}
</style>
