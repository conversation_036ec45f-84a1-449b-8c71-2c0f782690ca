<template>
  <div
    ref="containerRef"
    :class="containerClass"
    :style="containerStyle"
    @click="$emit('click', $event)"
  >
    <span ref="textRef" :style="textStyle">
      <slot>{{ text }}</slot>
    </span>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick, onMounted } from "vue";

interface Props {
  text?: string;
  maxFontSize?: number;
  minFontSize?: number;
  containerWidth?: number;
  containerHeight?: number;
  padding?: number;
  fontWeight?: string | number;
  fontFamily?: string;
  containerClass?: string;
  containerStyle?: Record<string, any>;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  text: "",
  maxFontSize: 14,
  minFontSize: 8,
  containerWidth: 92,
  containerHeight: 36,
  padding: 10,
  fontWeight: "700",
  fontFamily: "Inter",
  containerClass: "",
  containerStyle: () => ({}),
  disabled: false,
});

const emit = defineEmits<{
  click: [event: MouseEvent];
  fontSizeChanged: [fontSize: number];
}>();

const containerRef = ref<HTMLElement | null>(null);
const textRef = ref<HTMLElement | null>(null);
const currentFontSize = ref(props.maxFontSize);

// 计算可用宽度（减去padding）
const availableWidth = computed(() => {
  return props.containerWidth - props.padding * 2;
});

// 文本样式
const textStyle = computed(() => ({
  fontSize: currentFontSize.value + "px",
  fontWeight: props.fontWeight,
  fontFamily: props.fontFamily,
  transition: "font-size 0.2s ease",
}));

// 自适应字体大小函数
const adjustFontSize = () => {
  if (!containerRef.value || props.disabled) return;

  const text = props.text || containerRef.value.textContent || "";
  if (!text.trim()) return;

  let fontSize = props.maxFontSize;
  const minFontSize = props.minFontSize;
  const maxWidth = availableWidth.value;

  // 创建临时元素来测量文本宽度
  const tempElement = document.createElement("div");
  tempElement.style.position = "absolute";
  tempElement.style.visibility = "hidden";
  tempElement.style.whiteSpace = "nowrap";
  tempElement.style.fontWeight = String(props.fontWeight);
  tempElement.style.fontFamily = props.fontFamily;
  tempElement.textContent = text;
  document.body.appendChild(tempElement);

  try {
    // 逐步减小字体大小直到文本适合容器
    while (fontSize >= minFontSize) {
      tempElement.style.fontSize = fontSize + "px";
      const textWidth = tempElement.offsetWidth;

      if (textWidth <= maxWidth) {
        break;
      }
      fontSize -= 0.5;
    }

    // 应用计算出的字体大小
    const finalSize = Math.max(fontSize, minFontSize);
    currentFontSize.value = finalSize;

    // 触发字体大小变化事件
    emit("fontSizeChanged", finalSize);

    console.log(`AutoResizeText - Text: "${text}", Font size: ${finalSize}px`);
  } finally {
    // 清理临时元素
    document.body.removeChild(tempElement);
  }
};

// 监听文本变化
watch(
  () => props.text,
  () => {
    nextTick(() => {
      adjustFontSize();
    });
  }
);

// 监听容器尺寸变化
watch([() => props.containerWidth, () => props.padding], () => {
  nextTick(() => {
    adjustFontSize();
  });
});

// 组件挂载后初始化
onMounted(() => {
  nextTick(() => {
    adjustFontSize();
  });
});

// 暴露方法供外部调用
defineExpose({
  adjustFontSize,
  getCurrentFontSize: () => currentFontSize.value,
});
</script>

<style lang="scss" scoped>
.auto-resize-text {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  overflow: hidden;
}
</style>
