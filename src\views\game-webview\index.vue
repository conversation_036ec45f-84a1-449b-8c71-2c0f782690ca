<!-- filepath: /src/views/game-webview/index.vue -->
<template>
  <div class="iframe-container">
    <!-- Loading 组件：传递真实的加载进度 -->
    <div v-if="isLoading">
      <Loading :progress="loadingProgress" @back="handleBack" />
    </div>
    <!-- iframe 容器，v-show 控制加载完成后显示 -->
    <div class="iframe-wrapper">
      <iframe ref="iframeRef" :src="iframeUrl" @load="onIframeLoad" @error="onIframeError" frameborder="0"
        scrolling="auto" class="iframe-element">
      </iframe>
    </div>
    <!-- 悬浮按钮，仅在加载完成后显示 -->
    <div v-show="!isLoading">
      <ZFloatingBubble :left="10" :top="100" @click="handleBack">
        <div class="drap-icon">
          <ZIcon type="icon-fanhui2" :size="32" />
        </div>
      </ZFloatingBubble>
      <ZFloatingBubble :right="10" :top="300" @click="handleCustomerService">
        <div class="drap-icon">
          <ZIcon type="icon-kefu1" :size="32" />
        </div>
      </ZFloatingBubble>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { serviceMgr, ServiceType } from "@/utils/ServiceMgr";
import { gameItemLogin } from "@/api/games";
import { getGlobalDialog } from "@/enter/vant";
import { MAINTENANCETIPCODE } from '@/utils/config/GlobalConstant';
import { useGlobalStore } from "@/stores/global";
import { showToast } from 'vant';
import Loading from './Components/Loading.vue';
import ZFloatingBubble from '@/components/ZFloatingBubble/index.vue';
import { handleException } from '@/utils/JumpGame'

const $dialog = getGlobalDialog();
const globalStore = useGlobalStore();
const route = useRoute();
const router = useRouter();
const { id, company_id, third_game_id } = route.query;

const iframeRef = ref<HTMLIFrameElement | null>(null);
const isLoading = ref(true);
const iframeUrl = ref('');
const loadingProgress = ref(0);
const isIframeReady = ref(false); // 标记 iframe 是否准备好接收内容
let loadingTimeout: NodeJS.Timeout | null = null; // 加载超时定时器

const init = async () => {
  const params = { third_game_id, company_id, game_id: id };

  // 开始请求游戏数据，进度到 20%
  loadingProgress.value = 20;

  try {
    const response = await gameItemLogin(params);
    const isPass = handleException(response);
    if (!isPass) return;

    // 游戏数据获取成功，进度到 40%
    loadingProgress.value = 40;
    handleGameURL(response);
  } catch (err: any) {
    const isPass = handleException(err);
    if (!isPass) return;

    // 即使出错也尝试处理，进度到 40%
    loadingProgress.value = 40;
    handleGameURL(err);
  }
};

const handleGameURL = (response: any) => {
  if (!response) return;

  if (response.game_html) {
    // 准备加载 HTML 内容，进度到 60%
    loadingProgress.value = 60;

    const iframeDom = iframeRef.value;
    if (iframeDom) {
      const iframeDoc = iframeDom.contentWindow?.document;
      if (iframeDoc) {
        iframeDoc.open();
        iframeDoc.write(response.game_html);
        iframeDoc.close();

        // 标记 iframe 已准备好，HTML 内容写入完成，进度到 90%
        isIframeReady.value = true;
        loadingProgress.value = 90;

        // 设置超时机制，如果 10 秒内没有触发 onload，强制完成
        startLoadingTimeout();
      }
    }
  } else if (response.game_url) {
    // 准备加载 URL，进度到 60%
    loadingProgress.value = 60;

    // 开始加载 iframe URL，进度到 80%
    setTimeout(() => {
      loadingProgress.value = 80;
      isIframeReady.value = true;
      iframeUrl.value = response.game_url;

      // 设置超时机制，如果 10 秒内没有触发 onload，强制完成
      startLoadingTimeout();
    }, 200);
  }
};

// 开始加载超时计时
const startLoadingTimeout = () => {
  // 清除之前的定时器
  if (loadingTimeout) {
    clearTimeout(loadingTimeout);
  }

  // 设置 10 秒超时
  loadingTimeout = setTimeout(() => {
    if (isLoading.value) {
      loadingProgress.value = 100;
      setTimeout(() => {
        isLoading.value = false;
      }, 300);
    }
  }, 10000);
};

// 清除加载超时计时
const clearLoadingTimeout = () => {
  if (loadingTimeout) {
    clearTimeout(loadingTimeout);
    loadingTimeout = null;
  }
};

onMounted(() => {
  isLoading.value = true;
  loadingProgress.value = 0;

  // 开始初始化，进度到 10%
  setTimeout(() => {
    loadingProgress.value = 10;
    init();
  }, 100);
});

const handleBack = () => {
  if (window.history.length > 1) {
    router.back();
  } else {
    router.push('/');
  }
};

const handleCustomerService = () => {
  serviceMgr.instance.openChat(ServiceType.Game);
};

// 当 iframe 加载完成时，进度到 100% 然后隐藏 Loading
const onIframeLoad = () => {
  // 只有在 iframe 有实际内容时才处理加载完成事件
  if (iframeUrl.value || isIframeReady.value) {
    clearLoadingTimeout(); // 清除超时定时器
    loadingProgress.value = 100;

    // 短暂显示 100% 后隐藏 Loading
    setTimeout(() => {
      isLoading.value = false;
    }, 300);
  }
};

// 当 iframe 加载错误时，直接隐藏 Loading
const onIframeError = () => {
  if (iframeUrl.value || isIframeReady.value) {
    clearLoadingTimeout(); // 清除超时定时器
    loadingProgress.value = 100;
    setTimeout(() => {
      isLoading.value = false;
    }, 300);
  }
};


onUnmounted(() => {
  // 清理定时器
  clearLoadingTimeout();
});
</script>

<style scoped lang="scss">
.drap-icon {
  background-color: rgba(220, 220, 220, 0.2);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.iframe-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background-color: #000;

  .iframe-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .iframe-element {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
}
</style>
