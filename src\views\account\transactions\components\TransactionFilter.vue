<template>
  <div class="header-filter">
    <button @click="$emit('filter-click')" :class="{ active: status !== 'All' }" data-key="Status">
      <span>{{ status }}</span>
      <span v-if="status === 'All'" class="iconfont icon-xiangxia"></span>
      <span v-else @click.stop="$emit('clear-filter', $event)" class="iconfont icon-close"></span>
    </button>
  </div>
</template>

<script setup lang="ts">
interface Props {
  status: string;
}

defineProps<Props>();

defineEmits<{
  "filter-click": [];
  "clear-filter": [event: Event];
}>();
</script>

<style lang="scss" scoped></style>
