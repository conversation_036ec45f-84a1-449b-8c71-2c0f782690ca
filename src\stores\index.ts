// store/index.js
import { createPinia } from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";

export const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

// 导出所有 stores
export { useGlobalStore } from "./global";
export { useGameStore } from "./game";
export { useBannerStore } from "./banner";
export { useDepositStore } from "./deposit";
export { useWithdrawStore } from "./withdraw";
export { useKycStore } from "./kyc";
export { useAutoPopMgrStore } from "./autoPopMgr";
export { useRewardWalletStore } from "./rewardWallet";
